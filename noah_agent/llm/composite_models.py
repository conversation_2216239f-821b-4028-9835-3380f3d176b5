from config import settings
from llm.ali_models import <PERSON>wen3, SiliconFlowQwen3
from llm.azure_models import GPT41, GPTo4Mini, GPTo3
from llm.openai_models import Openaio4Mini, Openaio3
from llm.deepseek_models import DeepseekChat, <PERSON>oshanDeepseekChat, SiliconflowDeepseekChat
from llm.gcp_models import Gemini25<PERSON><PERSON>, Gemini25Pro, ClaudeSonnet4
from llm.base_model import CompositeModel


class SlotFillingModels(CompositeModel):
    def __init__(self, **params) -> None:
        self.models = [GPTo4Mini(), HuoshanDeepseekChat(**params), GPTo3(), DeepseekChat(**params), Qwen3(**params)]
        super().__init__()

class IDSelectionModels(CompositeModel):
    def __init__(self, **params) -> None:
        self.models = [HuoshanDeepseekChat(**params), GPTo4Mini(), GPT41(), Qwen3(**params)]
        super().__init__()
        
class GPT41Deepseek(CompositeModel):
    provider = "openai"
    
    models = [GPT41(), <PERSON>oshanDeepseekChat()]

class Compositeo4mini(CompositeModel):
    provider = "openai"
    
    def __init__(self, **params) -> None:
        if settings.ENV == 'local':
            self.models = [GPTo4Mini(reasoning_effort='medium'), GPT41()]
        else:
            self.models = [GPTo4Mini(reasoning_effort='medium'), Openaio4Mini(reasoning_effort='medium'), GPT41()]
        super().__init__()

class Compositeo3(CompositeModel):

    provider = "openai"
    
    def __init__(self, **params) -> None:
        if settings.ENV == 'local':
            self.models = [GPTo3(), GPT41()]
        else:
            self.models = [Openaio3(), GPTo3(), GPT41(), GPTo4Mini()]
        super().__init__()    


class CompositeHitlFinal(CompositeModel):

    provider = ""
    def __init__(self, **params) -> None:
        if settings.ENV == 'local':
            self.models = [Gemini25Pro(), GPT41()]
        else:
            self.models = [Gemini25Pro(), Openaio3(), ClaudeSonnet4(), GPT41()]
        super().__init__()


class CompositeMindsearchFinal(CompositeModel):

    provider = ""
    models = [GPT41(), Gemini25Flash()]
