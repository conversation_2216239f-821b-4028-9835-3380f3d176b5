from openai import Async<PERSON>penA<PERSON>
from config import api_config
from llm.azure_models import OpenAIModel, OpenAIReasoningModel, low_timeout_0_retry

class Openaio4Mini(OpenAIReasoningModel):
    
    def __init__(self, reasoning_effort: str="high") -> None:
        self.client = AsyncOpenAI(api_key=api_config.OPENAI_API_KEY,
                                  **low_timeout_0_retry)
        self.reasoning_effort  = reasoning_effort
        super().__init__(model=api_config.OPENAI_GPTo4_MINI_MODEL)


class Openaio3(OpenAIReasoningModel):

    def __init__(self, reasoning_effort: str="medium") -> None:
        self.client = AsyncOpenAI(api_key=api_config.OPENAI_API_KEY,
                                  **low_timeout_0_retry)
        self.reasoning_effort  = reasoning_effort
        super().__init__(model=api_config.OPENAI_GPTo3_MODEL)