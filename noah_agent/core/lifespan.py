from fastapi import <PERSON><PERSON><PERSON>
from contextlib import asynccontextmanager

from noah_agent.llm.azure_models import GPT4o, GPT41, GPT4o<PERSON>ini, GPTo1, GPTo1<PERSON><PERSON>, GPTo3Mini, GPTo3, <PERSON>To4<PERSON><PERSON>, GPT4oWorkflow, <PERSON>
from utils.core.httpx_client import HttpxClient<PERSON>ingleton
from llm.gcp_models import GeminiClientSingleton
from config import api_config
import httpx

granular_timeout = httpx.Timeout(45, connect=10.0)
low_timeout_0_retry = {"timeout": granular_timeout, "max_retries": 0}


@asynccontextmanager
async def lifespan(app: FastAPI):
    # Initialize clients
    HttpxClientSingleton.initialize()
    
    GeminiClientSingleton.initialize()

    GPT4o.initialize(
        api_key=api_config.AZURE_GPT4_OPENAI_API_KEY,
        api_version=api_config.AUZRE_GPT4o_MIN_VERSION,
        azure_endpoint=api_config.AZURE_GPT4_AZURE_ENDPOINT
    )
    GPT41.initialize(
        api_key=api_config.AZURE_GPTo1_OPENAI_API_KEY,
        api_version=api_config.AZURE_GPT4_1_VERSION,
        azure_endpoint=api_config.AZURE_GPTo1_AZURE_ENDPOINT, 
        **low_timeout_0_retry
    )
    GPT4oMini.initialize(
        api_key=api_config.AZURE_GPT4_OPENAI_API_KEY,
        api_version=api_config.AUZRE_GPT4o_MIN_VERSION,
        azure_endpoint=api_config.AZURE_GPT4_AZURE_ENDPOINT
    )
    GPTo1.initialize(
        api_key=api_config.AZURE_GPTo1_OPENAI_API_KEY,
        api_version=api_config.AZURE_GPTo1_VERSION,
        azure_endpoint=api_config.AZURE_GPTo1_AZURE_ENDPOINT,
        **low_timeout_0_retry
    )
    GPTo1Mini.initialize(
        api_key=api_config.AZURE_GPTo1_OPENAI_API_KEY,
        api_version=api_config.AZURE_GPTo1_MINI_VERSION,
        azure_endpoint=api_config.AZURE_GPTo1_AZURE_ENDPOINT,
        **low_timeout_0_retry
    )
    GPTo3Mini.initialize(
        api_key=api_config.AZURE_GPTo1_OPENAI_API_KEY,
        api_version=api_config.AZURE_GPTo3_MIN_VERSION,
        azure_endpoint=api_config.AZURE_GPTo1_AZURE_ENDPOINT,
        **low_timeout_0_retry
    )
    GPTo3.initialize(
        api_key=api_config.AZURE_GPTo1_OPENAI_API_KEY,
        api_version=api_config.AZURE_GPTo3_VERSION,
        azure_endpoint=api_config.AZURE_GPTo1_AZURE_ENDPOINT,
        **low_timeout_0_retry
    )
    GPTo4Mini.initialize(
        api_key=api_config.AZURE_GPTo4_MIN_API_KEY,
        api_version=api_config.AZURE_GPTo4_MIN_VERSION,
        azure_endpoint=api_config.AZURE_GPTo4_MIN_ENDPOINT,
        **low_timeout_0_retry
    )
    GPT4oWorkflow.initialize(
        api_key=api_config.AZURE_GPT4_OPENAI_API_KEY,
        api_version=api_config.AZURE_GPT4_OPENAI_API_VERSION,
        azure_endpoint=api_config.AZURE_GPT4_AZURE_ENDPOINT,
        **low_timeout_0_retry
    )

    Ada.initialize(
        api_key=api_config.AZURE_ADA002_OPENAI_API_KEY,
        api_version=api_config.AZURE_ADA002_OPENAI_API_VERSION,
        azure_endpoint=api_config.AZURE_ADA002_AZURE_ENDPOINT,
        azure_deployment=api_config.AZURE_ADA002_AZURE_DEPLOYMENT
    )
    yield

    # Cleanup clients
    HttpxClientSingleton.cleanup()

    await GeminiClientSingleton.cleanup()

    await GPT4o.cleanup(
        api_key=api_config.AZURE_GPT4_OPENAI_API_KEY,
        api_version=api_config.AUZRE_GPT4o_MIN_VERSION,
        azure_endpoint=api_config.AZURE_GPT4_AZURE_ENDPOINT
    )
    await GPT41.cleanup(
        api_key=api_config.AZURE_GPTo1_OPENAI_API_KEY,
        api_version=api_config.AZURE_GPT4_1_VERSION,
        azure_endpoint=api_config.AZURE_GPTo1_AZURE_ENDPOINT, 
        **low_timeout_0_retry
    )
    await GPT4oMini.cleanup(
        api_key=api_config.AZURE_GPT4_OPENAI_API_KEY,
        api_version=api_config.AUZRE_GPT4o_MIN_VERSION,
        azure_endpoint=api_config.AZURE_GPT4_AZURE_ENDPOINT
    )
    await GPTo1.cleanup(
        api_key=api_config.AZURE_GPTo1_OPENAI_API_KEY,
        api_version=api_config.AZURE_GPTo1_VERSION,
        azure_endpoint=api_config.AZURE_GPTo1_AZURE_ENDPOINT,
        **low_timeout_0_retry
    )
    await GPTo1Mini.cleanup(
        api_key=api_config.AZURE_GPTo1_OPENAI_API_KEY,
        api_version=api_config.AZURE_GPTo1_MINI_VERSION,
        azure_endpoint=api_config.AZURE_GPTo1_AZURE_ENDPOINT,
        **low_timeout_0_retry
    )
    await GPTo3Mini.cleanup(
        api_key=api_config.AZURE_GPTo1_OPENAI_API_KEY,
        api_version=api_config.AZURE_GPTo3_MIN_VERSION,
        azure_endpoint=api_config.AZURE_GPTo1_AZURE_ENDPOINT,
        **low_timeout_0_retry
    )
    await GPTo3.cleanup(
        api_key=api_config.AZURE_GPTo1_OPENAI_API_KEY,
        api_version=api_config.AZURE_GPTo3_VERSION,
        azure_endpoint=api_config.AZURE_GPTo1_AZURE_ENDPOINT,
        **low_timeout_0_retry
    )
    await GPTo4Mini.cleanup(
        api_key=api_config.AZURE_GPTo4_MIN_API_KEY,
        api_version=api_config.AZURE_GPTo4_MIN_VERSION,
        azure_endpoint=api_config.AZURE_GPTo4_MIN_ENDPOINT,
        **low_timeout_0_retry
    )
    await GPT4oWorkflow.cleanup(
        api_key=api_config.AZURE_GPT4_OPENAI_API_KEY,
        api_version=api_config.AZURE_GPT4_OPENAI_API_VERSION,
        azure_endpoint=api_config.AZURE_GPT4_AZURE_ENDPOINT,
        **low_timeout_0_retry
    )

    Ada.cleanup(
        api_key=api_config.AZURE_ADA002_OPENAI_API_KEY,
        api_version=api_config.AZURE_ADA002_OPENAI_API_VERSION,
        azure_endpoint=api_config.AZURE_ADA002_AZURE_ENDPOINT,
        azure_deployment=api_config.AZURE_ADA002_AZURE_DEPLOYMENT
    )