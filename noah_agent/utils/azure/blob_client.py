import asyncio
import traceback
import os
from azure.storage.blob import BlobServiceClient
import logging

logger = logging.getLogger(__name__)


def upload_file(container, object_key, file_path):
    # Instantiate a new BlobServiceClient using a connection string
    connection_string = os.getenv('AZURE_STORAGE_CONNECTION_STRING')
    blob_service_client = BlobServiceClient.from_connection_string(connection_string)

    # Instantiate a new ContainerClient
    container_client = blob_service_client.get_container_client("nudata")

    try:
        # Instantiate a new BlobClient
        blob_client = container_client.get_blob_client(object_key)

        # [START upload_a_blob]
        # Upload content to block blob
        with open(file_path, "rb") as source:
            blob_client.upload_blob(source, blob_type="BlockBlob")
        # [END upload_a_blob]
    except Exception as e:
        logger.info(f"Exception occurred during blob upload, {e}")
        return False
    return True