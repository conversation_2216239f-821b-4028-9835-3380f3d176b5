import os
import transformers
import tiktoken
from typing import List


class Tokenizer:
    
    def __init__(self):
        r"""
        init local tokenizer 
        """
        PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        STATIC_DIR = os.path.join(PROJECT_ROOT, "static")
        deepseek_v3_tokenizer_path = os.path.join(STATIC_DIR, "tokenizer/deepseek-v3")
        self.deepseekv3_tokenizer = transformers.AutoTokenizer.from_pretrained(deepseek_v3_tokenizer_path, trust_remote_code=True)

    def _connect_messages(self, user_prompt: str, history_messages: List[dict] = None) -> str:
        if history_messages is None:
            history_messages = []
            
        user_message = {
            "role": "user",
            "content": user_prompt,
        }

        messages = history_messages + [user_message]

        full_prompt = ""
        for message in messages:
            full_prompt += f"{message['role']}: {message['content']}\n"

        return full_prompt

    def deepseek_v3(self, user_prompt: str, history_messages: List[dict] = None):
        if history_messages is None:
            history_messages = []
        return self.deepseekv3_tokenizer.encode(self._connect_messages(user_prompt, history_messages))

    def openai(self, user_prompt: str, history_messages: List[dict] = None):
        if history_messages is None:
            history_messages = []
        enc = tiktoken.encoding_for_model("gpt-4o")
        return enc.encode(self._connect_messages(user_prompt, history_messages))
    
    def get_token_count(self, text: str, model: str) -> int:
        """Get the actual token count for a given text."""
        if 'deepseek' in model:
            return len(self.deepseekv3_tokenizer.encode(text))
        elif 'openai' in model:
            enc = tiktoken.encoding_for_model("gpt-4o")
            return len(enc.encode(text))
        else:
            raise ValueError(f"Unsupported model: {model}")
    
    def calculate_token_ratio(self, text: str, model: str) -> float:
        """Calculate the ratio of characters to tokens for a given text."""
        tokens = self.get_token_count(text, model)
        chars = len(text)
        return chars / tokens if tokens > 0 else 1

    def truncate_by_tokens(self, text: str, max_tokens: int, model: str) -> str:
        """Truncate text to fit within max_tokens with iterative refinement."""
        current_tokens = self.get_token_count(text, model)
        
        # If already within limit, return as is
        if current_tokens <= max_tokens:
            return text
        
        # Initial estimate using character ratio
        token_ratio = self.calculate_token_ratio(text, model)
        estimated_char_limit = int(max_tokens * token_ratio * 0.9)  # 90% to be more conservative
        
        # Start with estimated truncation
        truncated = text[:estimated_char_limit]
        
        # Iteratively adjust to meet token limit
        max_iterations = 10  # Prevent infinite loops
        iteration = 0
        
        while iteration < max_iterations:
            current_tokens = self.get_token_count(truncated, model)
            
            if current_tokens <= max_tokens:
                # Try to add more text if we're significantly under the limit
                if current_tokens < max_tokens * 0.95:  # If we're using less than 95% of tokens
                    # Calculate how much more we can potentially add
                    remaining_tokens = max_tokens - current_tokens
                    current_ratio = len(truncated) / current_tokens
                    additional_chars = int(remaining_tokens * current_ratio * 0.8)  # Conservative estimate
                    
                    new_length = min(len(truncated) + additional_chars, len(text))
                    if new_length > len(truncated):
                        new_truncated = text[:new_length]
                        new_tokens = self.get_token_count(new_truncated, model)
                        if new_tokens <= max_tokens:
                            truncated = new_truncated
                
                break
            else:
                # Reduce by proportion
                reduction_ratio = max_tokens / current_tokens * 0.95  # 95% to be safe
                new_length = int(len(truncated) * reduction_ratio)
                truncated = text[:new_length]
                
            iteration += 1
        
        # Final check and emergency truncation if needed
        final_tokens = self.get_token_count(truncated, model)
        if final_tokens > max_tokens:
            # Emergency: reduce by fixed percentage until it fits
            while final_tokens > max_tokens and len(truncated) > 0:
                truncated = truncated[:int(len(truncated) * 0.9)]
                final_tokens = self.get_token_count(truncated, model)
        
        return truncated

    def truncate_messages_by_tokens(self, user_prompt: str, history_messages: List[dict], 
                                  max_tokens: int, model: str) -> tuple:
        """
        Truncate the full conversation (history + user prompt) to fit within token limit.
        Returns (truncated_user_prompt, truncated_history_messages)
        """
        # First, try to fit everything
        full_text = self._connect_messages(user_prompt, history_messages)
        full_tokens = self.get_token_count(full_text, model)
        
        if full_tokens <= max_tokens:
            return user_prompt, history_messages
        
        # If it doesn't fit, prioritize user prompt and truncate history
        user_tokens = self.get_token_count(f"user: {user_prompt}\n", model)
        
        if user_tokens >= max_tokens:
            # User prompt itself is too long, truncate it
            truncated_prompt = self.truncate_by_tokens(user_prompt, max_tokens - 10, model)  # Reserve some tokens for formatting
            return truncated_prompt, []
        
        # Truncate history to fit remaining tokens
        remaining_tokens = max_tokens - user_tokens
        
        # Try to keep as much history as possible, starting from the most recent
        truncated_history = []
        current_tokens = 0
        
        for message in reversed(history_messages):
            message_text = f"{message['role']}: {message['content']}\n"
            message_tokens = self.get_token_count(message_text, model)
            
            if current_tokens + message_tokens <= remaining_tokens:
                truncated_history.insert(0, message)  # Insert at beginning to maintain order
                current_tokens += message_tokens
            else:
                # Try to partially include this message if there's remaining space
                if remaining_tokens - current_tokens > 50:  # Only if significant space remains
                    partial_content = self.truncate_by_tokens(
                        message['content'], 
                        remaining_tokens - current_tokens - 20,  # Reserve tokens for role formatting
                        model
                    )
                    if partial_content:
                        partial_message = {
                            "role": message['role'],
                            "content": partial_content
                        }
                        truncated_history.insert(0, partial_message)
                break
        
        return user_prompt, truncated_history


tokenizer = Tokenizer()