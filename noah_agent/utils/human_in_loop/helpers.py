import asyncio
import copy
import csv
import io
import json
import re
import traceback
from typing import Callable
import logging
import os
from datetime import datetime
from agent.human_in_loop.constants import background_prefix_map, data_empty_prompt, data_empty_prompt_cn
from tools.human_in_loop.planning.prompt import planning_final_template_cn, planning_final_template_en, planning_input_prompt, replanning_input_prompt, planning_input_prompt_cn, replanning_input_prompt_cn 
import pandas as pd

logger = logging.getLogger(__name__)

async def function_call_with_retry(f: Callable, *args, **kwargs):
    latest_exc = Exception("Function call failed after all retries")
    planning = kwargs.pop('planning', False)
    for attempt in range(5):
        try:
            function_call_response = await f(*args, **kwargs)
            if hasattr(function_call_response, 'tool_calls') and len(function_call_response.tool_calls) > 0:
                arguments = function_call_response.tool_calls[0].function.arguments
            else:
                try:
                    arguments = function_call_response.content
                except:
                    arguments = function_call_response[0]['arguments']
            # Handle case where arguments is a string (containing JSON)
            if isinstance(arguments, str):
                # Find the content between first { and last }
                # Try to find JSON enclosed in ```json{...}``` format
                if '```json' in arguments:
                    match = arguments.split('```json')[1].split('```')[0].strip()
                    arguments = match
                # Otherwise continue with the original approach
                start_idx = arguments.find('{')
                end_idx = arguments.rfind('}')
                if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
                    arguments = arguments[start_idx:end_idx+1]
            arguments = json.loads(arguments)
            while type(arguments) == list and arguments:
                arguments = arguments[0]
            while type(arguments) == dict and 'arguments' in arguments:
                arguments = arguments['arguments']
            if type(arguments) != dict:
                raise Exception("Invalid arguments format, should be dict")
            if planning and ('planned_sequence' not in arguments or not arguments['planned_sequence']) and ('additional_steps' not in arguments or not arguments['additional_steps']):
                raise Exception("Empty planned sequence")
            return arguments
        except Exception as e:
            latest_exc = e
            logger.error(f"Function call {f} failed: {e}")
            await asyncio.sleep(1)
            print('tool_slot_filling failed, retrying....')
            if hasattr(f, '_try_next_model') and callable(getattr(f, '_try_next_model')) and attempt % 2 == 1:
                if not f._try_next_model():
                    logger.error("No more models to try, giving up.")
                    break
    if str(latest_exc) == "Empty planned sequence":
        return {}
    raise latest_exc

def save_to_file(data: dict, output_dir, file_name: str):
    os.makedirs(output_dir, exist_ok=True)
    file_path = os.path.join(output_dir, file_name)
    if type(data) == str:
        # Check if data has line with only "---" in it and remove it
        data = re.sub(r'^---\s*$', '', data, flags=re.MULTILINE)
        with open(file_path, 'w', newline='', encoding='utf-8') as f:
            f.write(data)
    else:
        with open(file_path, 'w', encoding='utf-8') as f:
            try: json.dump(data, f, indent=4, ensure_ascii=False)
            except: f.write(data)
    print(f"- {file_path} saved successfully.")
    
def tool_history_to_prompt(prior_tool_use) -> str:
    if not prior_tool_use:
        return ""
    url_map = {} # url -> id to make sure llm get sequence reference
    prompt = "Tool use results:\n"
    number = 1
    for _, tool_use in enumerate(prior_tool_use):
        tool_use_result = ""
        if not tool_use.get('result', None):
            continue
        try:
            if tool_use['tool'] == "User-Question" and tool_use.get('question', None):
                prompt += f"{number}: User asked question: {tool_use['question']}\n"
                number += 1
                continue
            if tool_use['tool'] in ['Self-Reflection', 'Plan-Sequence']:
                continue
            if type(tool_use['result']) == str:
                tool_use_result = tool_use['result']
            else:
                tool_use_result = str(tool_use['result'].get('content', '') or tool_use['result'])
        except Exception as e:
            trace = traceback.format_exc()
            logger.info(f"Error parsing tool use result: {e}, Trace: {trace}")
        # update reference id
        tool_use_result = format_prompt_reference(tool_use_result, url_map)
        # format tool result
        tool_prompt = f"#{number}. Used tool:\n {tool_use['tool']}\n #Result:\n {tool_use_result}\n"
        if 'params' in tool_use and tool_use['params']:
            tool_prompt += f"#Params used:\n {tool_use['params']}"
        prompt += f"<tool>{tool_prompt}</tool>\n\n"
        number += 1
    return prompt

def format_prompt_reference(prompt: str, url_map: dict) -> str:

    r"Since each tool's reference starts from 1, we sort reference index"
    
    url_pattern = r'\[(.*?)\]\(([\w+.-]+:[^\s\)]+)\)'

    def replace_func(match):
        url = match.group(2)
        if url not in url_map:
            url_map[url] = len(url_map.items()) + 1
        id = url_map[url]
        return f"[{id}]({url})"

    return re.sub(url_pattern, replace_func, prompt)

def format_source(prior_tool_use: list[dict]) -> list:
    source = []
    source_url_set = set()
    url_map = {}
    for tool_use in prior_tool_use:
        tool_use_result = ""
        if not tool_use.get('result', None):
            continue
        if type(tool_use['result']) == str:
            tool_use_result = tool_use['result']
        else:
            tool_use_result = str(tool_use['result'].get('content', '') or tool_use['result'])

        # update url_map
        format_prompt_reference(tool_use_result, url_map)

        # get source object from current search_graph
        if type(tool_use['result']) == str:
            continue

        search_graph = tool_use['result'].get('search_graph', {})
        if not search_graph:
            continue
        tool_source = search_graph.get('source', [])
        for link in tool_source:
            if type(link) != dict:
                continue
            url = link.get('url', '')
            if url in source_url_set:
                continue
            source_url_set.add(url)
            if url in url_map:
                link['id'] = url_map[url]
                source.append(link)

    source.sort(key=lambda x: x['id'])
    return source

def build_planning_prompt(language, user_prompt, prior_tool_use = [], plan = [], feedback=[], MAX_STEPS = 4):
    template = planning_final_template_cn if language.lower() == 'cn' else planning_final_template_en
    instructions_prompt = replanning_input_prompt if len(plan)>1 else planning_input_prompt
    if language.lower() == 'cn': instructions_prompt = replanning_input_prompt_cn if len(plan)>1 else planning_input_prompt_cn
    prior_knowledge = tool_history_to_prompt(prior_tool_use)
    if plan:
        instructions_prompt = instructions_prompt.format(
            current_plan=str(plan), 
            completed_steps=list({'tool':tool['tool']} for tool in prior_tool_use),
            user_feedback=str(feedback)
        )
    kwargs = {
        'current_date': datetime.now().strftime('%Y-%m-%d'),
        'prior_tool_use': prior_tool_use,
        'user_prompt': user_prompt,
        'instructions_prompt': instructions_prompt,
        'prior_knowledge': prior_knowledge,
        'total_steps': MAX_STEPS
    }
    filled_template = template.format(**kwargs)
    return filled_template

def build_search_prompt(user_prompt, current_tool, prior_tool_use = []):
    reason = current_tool.get('reason', '')
    if reason:
        user_prompt += f"Reason for performing search: {reason}\n"
    current_question = (current_tool.get('params', None) or {}).get('question','') or reason or user_prompt
    user_prompt = f"{current_question}\n\n"
    user_prompt += tool_history_to_prompt(prior_tool_use)
    user_prompt += "\nNote: Please don't make assumptions on data (especially data that can easily be verified such as stock price and NCT ids) and don't make up citations, only use whatever context/data has been provided to you.\n"
    return user_prompt

def build_summary_prompt(user_prompt, current_tool, prior_tool_use=[], language='en'):
    if language == 'cn':
        prompt = f'''现在日期：{datetime.now().strftime('%Y-%m-%d')}
请完整地总结全部工具的使用结果并回答用户的原始问题： {user_prompt}。对于引用，尽量保留工具使用结果中的参考链接。
- 引用格式为Markdown格式：[id](原始来源链接)，例如：[1](https://www.medical-journal.com)[3](http://health/xx.html)。
- 不要在结尾处集中添加引用，例如：参考资料（引用文献） [2](https://www.drug.com/xx)[13](https://pubmed.articles.com/xx)
- 在必要时绘制表格，并配以详细解释。
**重要** 尽最大的力去包含工具使用结果中的详细信息和数据，使回应丰富且详尽。不要漏过任何重要信息。
'''
# - 在必要时绘制图表，并配以详细解释。使用mermaid或vega绘制图表。
    else:
        prompt = f'''Current date: {datetime.now().strftime('%Y-%m-%d')}
Please comprehensively summarize all tool use results and answer the user's original question: {user_prompt} based on the tool use results. For the citations, try to keep the refer link from the tool use results.
- The citation format is Markdown format: [id](original source link), i.e. [1](https://www.medical-journal.com)[2](http://health/xx.html).
- Don't group citations at the end of final response, like: Reference (Future Reading) [2](https://www.drug.com/xx)[13](https://pubmed.articles.com/xx)
- Draw tables wherever necessary, and pair them with detailed explanations.
**Important** Include as much detailed information and data from tool use results as possible and make the response as rich and lengthy as possible. Do not miss any important information.
'''
# - Draw graphs and tables wherever necessary, and pair them with detailed explanations. Use either mermaid or vega to draw graphs.
    
    prompt += tool_history_to_prompt(prior_tool_use)
    return prompt

def build_inference_prompt(user_prompt, current_tool, prior_tool_use=[], language='en'):
    current_question = (current_tool.get('params', None) or {}).get('question','')
    
    if language == 'cn':
        prompt = f'''您是"若生科技（Noah AI）"的医疗人工智能助手，擅长搜索和组织信息。您在医疗和金融领域拥有深厚的知识。
现在日期：{datetime.now().strftime('%Y-%m-%d')}
请根据目标：{current_tool.get("reason", "")}、工具使用历史、{f"用户现有问题：{current_question}、" if current_question else ""}用户的原始问题 {user_prompt} 生成回应。'''
    else:
        prompt = f'''You are a medical AI Assistant for `若生科技 (Noah AI)`, adept at searching for and organizing information. You possess profound knowledge in medical and finance fields.
Current date: {datetime.now().strftime('%Y-%m-%d')}
Generate a response considering the goal: {current_tool.get("reason", "")}, the tool use history, {f"the current question: {current_question}, " if current_question else ""}and the users original question {user_prompt}'''
    
    prompt += tool_history_to_prompt(prior_tool_use)
    return prompt

async def build_workflow_prompt(context_data, current_tool, language='en', output_dir='', current_step=0, total_feedback=[], prev_tool_uses=None, plan=[]):
    
    current_tool = copy.deepcopy(current_tool)
    plan = copy.deepcopy(plan)
    for step in plan:
        step.pop('result', None)
        step.pop('params', None)
    
    # Convert the JSON data to Excel format for easier viewing
    if isinstance(context_data, list) and context_data and isinstance(context_data[0], dict):
        # Handle case where the data is a list of dictionaries
        df = pd.DataFrame(context_data)
        # Convert lists to strings for better display in Excel
        for column in df.columns:
            if df[column].apply(lambda x: isinstance(x, list)).any():
                df[column] = df[column].apply(lambda x: ', '.join(x) if isinstance(x, list) and all(isinstance(item, str) for item in x) else x)
        
        if current_tool['tool'] == "Drug-Analysis":
            # Drop 'partner_companies' column if it exists
            if 'partner_companies' in df.columns:
                df = df.drop(columns=['partner_companies'])
                        
            # Rename 'lead_company' to 'company' if it exists
            if 'lead_company' in df.columns:
                df = df.rename(columns={'lead_company': 'company'})
        # Convert column names from snake_case to Title Case With Spaces
        df.columns = [' '.join(word.capitalize() for word in col.split('_')) for col in df.columns]
        
        # Create Excel writer with xlsxwriter engine
        excel_path = os.path.join(output_dir, f"{current_step}_{current_tool['tool']}_data.xlsx")
        os.makedirs(output_dir, exist_ok=True)
        
        with pd.ExcelWriter(excel_path, engine='xlsxwriter') as writer:
            # Write dataframe starting from row 3 (appears as 4th row)
            df.to_excel(writer, index=False, startrow=6)
            
            # Get the xlsxwriter workbook and worksheet objects
            workbook = writer.book
            worksheet = writer.sheets['Sheet1']
            
            # Insert logo in the first cell
            logo_path = 'static/logo-middle.png'
            if os.path.exists(logo_path):
                options = {'x_scale': 0.7, 'y_scale': 0.65, 'x_offset': 50, 'y_offset': 10}  # Scale to 90% of original size
                worksheet.insert_image('A1', logo_path, options)
            else:
                print(f"Warning: Logo file not found at {logo_path}")
                
            arial_format = workbook.add_format({'font_name': '等线'})
            worksheet.set_column(0, 100, None, arial_format)  # Apply Arial to all columns
            # Set the width of the first column to be 6 times the default width
            worksheet.set_column(0, 0, 66, arial_format)  # Default Excel column width is about 8.43 characters (64 pixels)
                                            # so 6x would be around 90 characters wide
            # worksheet.set_column(1, 1, 43, arial_format)  # Default Excel column width is about 8.43 characters (64 pixels)
                                        # so 6x would be around 90 characters wide

            text = "Noah AI-AI Agent Specialized in Life Science"
            text_2 = "https://www.noahai.co/about"
            # Add formatted text headers in rows 2 and 3
            text_format = workbook.add_format({
                'bold': False,
                'font_size': 12,
                'align': 'left',
                'font_name': '等线',
                'valign': 'vcenter'
            })
            url_format = workbook.add_format({
                'bold': False,
                'underline': True,  # Underline the text to indicate it's a URL
                'font_size': 12,
                'align': 'left',
                'valign': 'vcenter',
                'font_name': '等线',
                'font_color': '#467886'
            })

            # Insert the text into cell A2 (row 1, col 0 in zero-based indexing)
            worksheet.write(1, 3, text, text_format)

            # Insert the second text into cell A3 (row 2, col 0 in zero-based indexing)
            worksheet.write(2, 3, text_2, url_format)
            for col_num, column in enumerate(df.columns):
                if col_num < 3:
                    worksheet.set_column(col_num, col_num, 22, arial_format)
                if col_num >= 3:  # Column 3 is index 2 (zero-based)
                    # Get the maximum length in this column
                    column_width = min(
                        22,
                        max(df[column].astype(str).map(len).max(),len(column))
                    )
                    # Add some padding
                    column_width += 2
                    worksheet.set_column(col_num, col_num, column_width, arial_format)

        
        print(f"- {excel_path} saved successfully.")
    # save_to_file(context_data, output_dir, f"{current_step}_{current_tool['tool']}_data.json")
    
    extra_explanation = '(Please note the data selected is limited due to token limit, so the tool may not be able to answer the question completely. Take into account this limitation when generating the response.' 
    if current_tool['tool'] == "Drug-Analysis" and current_tool['params'].get('location', []):
        extra_explanation += f" Also the location is limited to {current_tool['params']['location']})"
    else: 
        extra_explanation += ')'
    
    background_prefix = background_prefix_map[current_tool['tool']] if current_tool['tool'] in background_prefix_map else ''
    background = f"{background_prefix}{extra_explanation} {context_data}" if context_data else data_empty_prompt_cn if language == 'cn' else data_empty_prompt
    body_user_prompt = f"We are using tool number {current_step}: {json.dumps(current_tool, separators=(',', ':'), ensure_ascii=False)} to: {current_tool.get('reason', '')}\n"
    total_feedback_prompt = ''
    if total_feedback:
        total_feedback_prompt = f"Consider the user's feedback: {total_feedback[-1]}\n"
    requirements_prompt = "Requirements: \n1. Do not make assumptions on data or make up data, only use whatever context/data has been provided to you."
    requirements_prompt += "\n2. State the limitations of the data provided if there are any."
    plan_prompt = f"Current plan: {json.dumps(plan, separators=(',', ':'), ensure_ascii=False)}\n\n" if plan else ''
    step_body = {
        "user_prompt": f"{plan_prompt}{body_user_prompt}{total_feedback_prompt}{requirements_prompt}",
        "history_messages": [],
        "agent":"mindsearchworkflowrefer",
        "skip_followup": True,
        "params":{
            "language": language,
            "model": "",
            "enable_rag": False,
            "background": background,
            }
    }
    if prev_tool_uses and context_data:
        pattern = r'(?:[#]+\s+[^\n]+\s*)?```vega[\s\S]*?```'
        # Replace all occurrences with an empty string
        text = tool_history_to_prompt(prev_tool_uses)
        cleaned_text = re.sub(pattern, '', text)
        step_body['params']['background'] += f'\n{cleaned_text}'
    return step_body

# async def perform_search(user_prompt, current_tool, prior_tool_use = [], language='en'):
#     search_prompt = build_search_prompt(user_prompt, {})
#     step_body = {
#         "user_prompt": search_prompt,
#         "history_messages": [],
#         "agent": "mindsearch",
#         "skip_followup": True,
#         "params":{
#             "language": language,
#             "model": "",
#             "enable_rag": True,
#             }
#     }
#     agent = MindSearchAgent()
#     generator = agent.start_wo_dump(**step_body)   
#     latest_chunk = ''
#     async for chunk in generator:
#         if not chunk:
#             continue
#         if type(chunk) == dict:
#             latest_chunk = chunk.get('content', '')
#         elif type(chunk) == str:
#             latest_chunk = chunk
#     return latest_chunk