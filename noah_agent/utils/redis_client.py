import redis

from config import settings

def get_connection(debug=False):
    global engine
    try:
        engine.ping()
        if debug: print('redis using main engine')
        return engine
    except Exception as exc:
        if debug: print('redis connect failed {exc}')
    return None


def create_engines(decode_responses=True):
    engine = redis.Redis(
        host=settings.REDIS_HOST,
        port=settings.REDIS_PORT,
        db=settings.REDIS_DB,
        decode_responses=decode_responses,
        socket_timeout=5,          # Socket timeout in seconds
        socket_connect_timeout=5,  # Connect timeout
        socket_keepalive=True,     # Keep connections alive
        health_check_interval=30   # Periodic health checks
    )
    return engine

engine = create_engines()
get_connection(debug=True)
print('---redis engine ready')

