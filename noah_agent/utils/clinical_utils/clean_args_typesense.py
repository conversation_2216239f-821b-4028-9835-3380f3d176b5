from functools import reduce
from utils.retrievers.typesense_retriever import search_one_column, get_client

def clean_args_typesense(args: dict, citeline = False, tool = '') -> dict:
    if tool in ["Drug-Analysis"]:
        citeline = True
    client = get_client()
    map_dict = {"indication_name": "indication", "target": "target", "lead_company":"company", "company": "company", "drug_name": "drug"}
    if citeline:
        map_dict.update({"target": "citeline_target", "company":"citeline_company", "drug_name": "citeline_drug", "indication_name": "citeline_indication"})
    for key in map_dict.keys():
        if key in args and args[key]:
            new_data = set()
            if type(args[key]) == dict: 
                items = reduce(lambda x, y: x + y, [item.split('/') for item in args[key].get("data",[])], [])
                for item in set(items):
                    search_result = search_one_column(client=client, name=map_dict[key], query=item, num_typos=1)
                    if search_result:
                        new_data.add(search_result)
                    else:
                        pass
                args[key]["data"] = list(new_data)
            elif type(args[key]) == list:
                items = reduce(lambda x, y: x + y, [item.split('/') for item in args[key]], [])
                for item in items:
                    search_result = search_one_column(client=client, name=map_dict[key], query=item, num_typos=1)
                    if search_result:
                        new_data.add(search_result)
                    else:
                        pass
                args[key] = list(new_data)
    if tool == "Drug-Analysis":
        args['location'] = args.get('location', []) or ['USA', 'China']
    return args