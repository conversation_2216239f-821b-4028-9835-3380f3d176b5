from ..core.interfaces import PromptTemplate
from datetime import datetime
import pytz
import json

class TechnicalAnalysisPrompt(PromptTemplate):
    """Technical analysis prompt template"""
    
    def __init__(self):
        self.template = """请对 {symbol} 的技术面进行全面分析。以下是最新的技术数据和市场信息：

{data_summary}

### 分析要求（严格遵循）
1. 所有结论必须标注数据来源，格式示例：
   - [价格数据: 36.04@2024-03-01]
   - [MACD: 0.5@2024-03-01]
   - [新闻: FDA批准@2024-02-28]

2. 按此优先级分析：
⚠️ 趋势方向 > 支撑阻力 > 成交量异常 > 技术指标

3. 必须包含：
   | 分析维度       | 输出要求                          |
   |----------------|---------------------------------|
   | 短期趋势       | 明确方向+置信度+时间范围          |
   | 关键支撑/阻力  | 3个价位+形成依据                  |
   | 风险预警       | 至少2个明确触发条件              |

### 禁止事项（重要！）
❌ 禁止使用任何未提供的数据
❌ 禁止模糊表述（必须注明时间/数值）
❌ 禁止省略数据来源标注

### 分析框架
1. 核心结论（置顶）
   - 用⚠️符号突出3个最关键结论
   - 每个结论必须包含：
     [数据依据] + [时间范围] + [置信度]

2. 详细分析
   - 每个段落开头用【数据来源】标注
   - 技术指标分析必须包含：
     指标名称 + 数值 + 计算逻辑

3. 情景预测（必须包含，用报告的格式输出）
   ``` 
   if 突破 '关键阻力位' then 看涨至'目标价'
   elif 跌破'关键支撑位' then 看跌至'目标价'
   else 维持震荡区间['支撑位'-'阻力位']
   ```

### 自检清单（生成后必须逐项检查）
[ ] 所有数值是否都有数据来源标注
[ ] 是否包含3个明确的关键结论
[ ] 是否包含三种情景分析
[ ] 是否标注了无法分析的条目"""

    def format(self, **kwargs) -> str:
        """Format prompt template with parameters"""
        return self.template.format(**kwargs)

class FinancialAnalysisPrompt(PromptTemplate):
    def format(self, symbol: str, financial_data: str, latest_price: float) -> str:
        eastern = pytz.timezone('US/Eastern')
        current_time = datetime.now(eastern)
        
        prompt = f"""# {symbol} 财务分析请求

以下是需要你分析的内容和我希望你产出的结果。请在回答时使用中文，保持分析的客观性和谨慎性。如果数据不足以支持某项结论，请明确指出。

## 基础信息
- 当前美国东部时间：{current_time.strftime('%Y-%m-%d %H:%M:%S %Z')}
- 当前股价：{latest_price}

## 可用财务数据
```json
{financial_data}
```

## 分析要求（严格遵循）

### 数据规范
⚠️ 所有数据引用必须使用标准格式：
- 财务报表数据 → [报表类型: 数值@时间] 例：[资产负债表: 现金$1.2B@2023-Q4]
- 电话会议内容 → [会议日期: 摘要] 例：[2024-02-28会议: 预计Q2营收增长15%]
- 行业对比数据 → [来源: 数值@时间] 例：[行业平均: PE 25@2024-03]

### 1. 收入结构分析
``` 
【必须包含】
1. 产品收入构成变化分析（需标注时间范围）
2. 地区收入战略意义（需标注具体地区）
3. 收入多元化评分（1-5分，需说明评分依据）
```

### 2. 现金状况分析
``` 
【计算要求】
现金消耗率 = (营业现金流绝对值) / 现金储备 → [计算过程必须展示]
安全周期 = 现金储备 / (营业现金流 + 投资现金流) → [计算过程必须展示]

【输出要求】
- 每个数值必须标注数据时间点
- 现金流分析必须区分经营/投资/融资活动
```

### 3. 研发投入分析
``` 
【分析维度】
1. 研发强度 = 研发支出 / 总收入 → [必须计算并标注时间范围]
2. 行业对比（如有数据需标注来源）
3. 管线投入占比 = 研发支出 / 总运营费用 → [必须计算]
```

### 4. 财务健康度评估
``` 
【必须分析】
1. 债务安全边际 = 现金储备 / 短期债务 → [必须计算]
2. 营运资本比率 = 流动资产 / 流动负债 → [必须计算]
3. 利息覆盖率 = EBIT / 利息支出 → [必须计算]
```

### 5. 投资建议
``` 
【必须包含】
1. 估值计算过程（示例）：
   PE估值 = [行业平均PE] × [公司EPS] → [需标注数据来源]
   DCF估值 = [现金流预测] / (1+[折现率])^n → [需说明假设条件]

2. 风险对冲方案：
   - 至少2个具体对冲策略
   - 每个策略需说明适用条件
```

## 禁止事项（重要！）
❌ 禁止使用未提供的财务比率（如ROIC/ROE等）
❌ 禁止模糊时间表述（如"近期""过去几年"）
❌ 禁止省略数据时间范围

## 自检清单（生成后必须检查）
[ ] 所有财务比率是否都有计算过程
[ ] 所有预测是否标注假设条件
[ ] 是否标注所有数据缺失项
[ ] 投资建议是否包含风险对冲方案

## 重要提醒
如遇以下情况请明确指出：
1. 关键数据缺失（如现金储备/营业现金流）
2. 数据时间超过12个月
3. 数据存在明显矛盾

请严格按照以上要求生成分析报告，所有结论必须可追溯原始数据。"""
        
        return prompt

class OptionsAnalysisPrompt(PromptTemplate):
    def format(self, symbol: str, options_data: str, latest_price: float) -> str:
        eastern = pytz.timezone('US/Eastern')
        current_time = datetime.now(eastern)
        
        return f"""# {symbol} 期权市场分析

## 基础信息
- 当前美国东部时间：{current_time.strftime('%Y-%m-%d %H:%M:%S %Z')}
- 当前股价：{latest_price}

## 期权数据
```json
{options_data}
```

## 分析规范（严格遵循）
### 数据引用格式
⚠️ 所有数据必须标注来源：
- 隐含波动率 → [IV: 值@到期日] 例：[IV: 45%@2024-03-15]
- 期权交易 → [合约类型: 数量@时间] 例：[Call: 5000@2024-03-01]
- 大额交易 → [大宗交易: 详情@时间]

### 1. 市场情绪分析
``` 
【必须包含】
1. 波动率结构：
   - IV历史分位值计算（需展示计算过程）
   - 看跌/看涨IV差异分析（需标注具体到期日）
   
2. 交易活跃度：
   - Put/Call比率计算（需展示原始数据）
   - 异常交易量识别（需标注时间戳）
```

### 2. 事件风险分析
``` 
【分析要求】
- 每个事件假设必须标注：
  [到期日] + [IV异常值] + [交易量变化]
- 风险溢价计算必须展示公式：
  风险溢价 = [看跌期权IV] - [历史波动率]
```

### 3. 机构策略分析
``` 
【必须识别】
1. 组合策略类型（跨式/宽跨式等）
2. 行权价集中度（计算前3大行权价占比）
3. 资金流向分析（需区分机构/散户）
```

### 4. 交易策略建议
``` 
【必须包含】
1. 每个策略必须标注：
   - 适用条件（需引用IV/Delta数据）
   - 最大亏损计算（需展示公式）
   - 盈亏平衡点计算
   
2. 对冲方案必须包含：
   - 对冲成本（需计算权利金支出）
   - 保护有效期（需标注到期日）
```

## 禁止事项（重要！）
❌ 禁止使用未提供的希腊字母数据（如Vega/Theta）
❌ 禁止模糊表述波动率水平（必须注明具体数值）
❌ 禁止省略数据时间范围

## 自检清单（生成后必须检查）
[ ] 所有IV值是否都有数据标注
[ ] 是否包含盈亏平衡点计算
[ ] 是否标注所有数据缺失项
[ ] 是否包含风险对冲方案

## 生物医药行业特殊要求
1. 重点分析以下信号：
   - 短期IV异常飙升（可能预示临床数据发布）
   - 深度虚值看涨期权交易（可能预示并购传闻）
   - 跨式组合集中度（反映市场对事件的预期）

2. 必须评估：
   - IV期限结构是否反映FDA审批日期
   - 看跌期权溢价是否包含监管风险
   - 大宗交易是否涉及内幕信息

## 重要提醒
1. 所有策略建议必须基于提供的期权链数据
2. 如发现以下情况请停止分析：
   - 缺少关键数据（如IV曲面/未平仓合约）
   - 数据时间超过7个交易日
   - 无法验证数据一致性

请严格按照以上要求生成分析报告，所有结论必须可追溯原始数据。"""

class CatalystAnalysisPrompt(PromptTemplate):
    """Catalyst事件分析模板"""
    
    def __init__(self):
        self.template = """请对 {company_name} 的催化剂事件进行全面分析。以下是最近的催化剂事件及最近重大事件中药品和适应症信息：

<Noah Data>
{catalyst_data}
</Noah Data>

## 分析规范（严格遵循）
所有的分析必须基于<Noah Data>中的数据，不允许编造任何数据。

### 1. 事件全景扫描 [!必须包含]
以表格的形式，展示未来将要发生的催化剂事件，包括事件内容，预计的发生时间，事件对公司的重要性（高/中/低）

### 2. 催化剂事件分析 [!深度分析]
基于<Noah Data>中的数据，对催化剂事件进行深度分析，分析包括催化剂相关药物的历史临床数据，竞争对手临床数据，竞争格局等：

#### 2.1 催化剂药物历史临床数据（如果<Noah Data>中提供）

<Noah Data>中提供的全部历史临床数据，包括试验设计，主要终点，次要终点，入组标准，疗效，安全性等。

#### 2.2 竞争对手临床数据（如果<Noah Data>中提供）

包括<Noah Data>中提供的竞争对手的全部临床数据，包括试验设计，主要终点，次要终点，入组标准，疗效，安全性等，以及竞争对手的临床数据对比分析。
分析时需要考虑不同试验之间的差异（患者特征，入组标准，主要终点，次要终点，患者人数等），以及不同试验之间的可比性。

#### 2.3 竞争格局分析

分析催化剂药物是否存在竞争，如果存在竞争，分析竞争格局，包括竞争对手的靶点、适应症、研发阶段，进度是否领先，是否存在临床数据优势等。

结合上述分析，判断催化剂事件正面和负面发生的概率，以及支持该概率的证据。

请严格按照以上要求尽量详细和全面地生成分析报告，所有数据必须来自<catalyst_data>，所有假设和计算必须在此部分明确标注：
## 假设与计算
[此处列出所有使用的假设和完整计算过程]"""

    def format(self, **kwargs) -> str:
        """Format prompt template with parameters"""
        return self.template.format(**kwargs)
   
class IncludingPastCatalystAnalysisPrompt(PromptTemplate):
    """Catalyst事件分析模板"""
    
    def __init__(self):
        self.template = """请对 {company_name} 的催化剂事件进行全面分析。以下是最近的催化剂事件及最近重大事件中药品和适应症信息：

<Noah Data>
{catalyst_data}
</Noah Data>

## 分析规范（严格遵循）
所有的分析必须基于<Noah Data>中的数据，不允许编造任何数据。

### 1. 事件全景扫描 [!必须包含]
以表格的形式，展示近期发生及未来将要发生的催化剂事件，包括事件内容，预计的发生时间，事件对公司的重要性（高/中/低）

### 2. 催化剂事件分析 [!深度分析]
基于<Noah Data>中的数据，对催化剂事件进行深度分析，分析包括催化剂相关药物的历史临床数据，竞争对手临床数据，竞争格局等：

#### 2.1 催化剂药物历史临床数据（如果<Noah Data>中提供）

<Noah Data>中提供的全部历史临床数据，包括试验设计，主要终点，次要终点，入组标准，疗效，安全性等。

#### 2.2 竞争对手临床数据（如果<Noah Data>中提供）

包括<Noah Data>中提供的竞争对手的全部临床数据，包括试验设计，主要终点，次要终点，入组标准，疗效，安全性等，以及竞争对手的临床数据对比分析。
分析时需要考虑不同试验之间的差异（患者特征，入组标准，主要终点，次要终点，患者人数等），以及不同试验之间的可比性。

#### 2.3 竞争格局分析

分析催化剂药物是否存在竞争，如果存在竞争，分析竞争格局，包括竞争对手的靶点、适应症、研发阶段，进度是否领先，是否存在临床数据优势等。

结合上述分析，判断催化剂事件正面和负面发生的概率，以及支持该概率的证据。

请严格按照以上要求生成详细和全面的分析报告，所有数据必须来自<catalyst_data>，所有假设和计算必须在此部分明确标注：
## 假设与计算
[此处列出所有使用的假设和完整计算过程]"""

    def format(self, **kwargs) -> str:
        """Format prompt template with parameters"""
        return self.template.format(**kwargs)
# class IntegratedReportPrompt:
#     def format(self, symbol: str, report_date: str, latest_price: float, market_cap: float, technical_analysis: str, financial_analysis: str, options_analysis: str = None, catalyst_analysis: str = None) -> str:
#         """Format prompt for generating integrated investment report
        
#         Args:
#             symbol: Stock symbol (formatted for display)
#             technical_analysis: Technical analysis result
#             financial_analysis: Financial analysis result
#             options_analysis: Options analysis result (optional)
#         """
#         return f"""请基于以下多个维度的分析结果，生成一份完整的投资分析报告。报告应该全面但重点突出，语言专业但易于理解，分析深入且具有前瞻性，建议明确且可操作。
#         请只使用提供的数据进行分析，并在使用时在结论旁标注数据来源（例如："根据财报数据，2024年收入为$100M，同比增长20%"，"根据电话会议记录，管理层预计2025年收入为$150M，同比增长15%"等）。所有假设和预测必须基于已有数据和合理推导，绝对绝对不要编造数据和幻觉，如果你需要的数据没有提供，请直接指出该部分数据缺失。如果你采用的数据是基于假设的，请在结论旁标注假设条件。

# # 分析对象
# {symbol}

# # 报告日期
# {report_date}

# # 最新价格
# {latest_price}

# # 市值
# {market_cap}

# # 技术分析结果
# {technical_analysis}

# # 财务分析结果
# {financial_analysis}

# # 催化剂事件分析结果（如有）
# {catalyst_analysis if catalyst_analysis else "无催化剂事件数据"}

# # 期权分析结果（如有）
# {options_analysis if options_analysis else "无期权数据"}

# 请按照以下结构生成最终报告：

# 1. 投资评级与目标价
#    - 明确给出投资评级（买入/增持/持有/减持/卖出）
#      * 基于三种情景给出12个月目标价：
#        * Bull Case：基于产品管线全部成功和市场份额最大化
#        * Base Case：基于当前发展轨迹和市场预期
#        * Bear Case：基于竞争加剧和研发不及预期

#    - 详细说明目标价的计算依据
#    - 列举3-5个最具说服力的投资理由
#    - 与市场一致预期的差异分析

# 2. 执行摘要
#    - 投资亮点：
#      * 产品商业化进展
#      * 研发管线里程碑
#      * 市场份额变化
#      * 财务指标改善
#    - 最新催化剂事件分析：
#      * 催化剂事件
#      * 催化剂事件正向和负向发生的概率和支持该概率的证据
#      * 催化剂事件对目标价的影响
#    - 风险提示：
#      * 研发风险
#      * 竞争风险
#      * 监管风险
#      * 财务风险

# 3. 核心产品分析
#    - 已上市产品：
#      * 市场份额
#      * 销售增长
#      * 竞争态势
#      * 专利保护
#    - 在研产品：
#      * 临床进展
#      * 市场潜力
#      * 成功概率
#      * 预计上市时间
#    - 催化剂事件：
#      * 催化剂事件和对近期重要事件的详细分析，包括相关产品详细的临床数据（设计，有效性，安全性等），分析和比较
#      * 催化剂事件正向和负向发生的概率和支持该概率的证据
#      * 催化剂事件对目标价的影响

# 4. 财务分析
#    - 收入结构：
#      * 产品收入占比
#      * 地区收入分布
#      * 增长驱动因素
#    - 盈利能力：
#      * 毛利率趋势
#      * 费用控制
#      * 现金流状况
#    - 研发投入：
#      * 投入规模
#      * 资金来源
#      * 重点项目

# 5. 技术面分析
#    - 趋势研判：
#      * 主要趋势方向
#      * 趋势强度
#      * 关键支撑位
#      * 重要阻力位
#    - 成交量分析：
#      * 量价配合
#      * 机构动向
#      * 异常交易
#    - 市场情绪：
#      * 技术指标
#      * 期权市场反映
#      * 机构持仓变化

# 6. 交易建议
#    ## 建仓策略
#    - 目标价位区间
#    - 分批建仓比例
#    - 建仓时机选择
#    - 风险控制措施

#    ## 持仓管理
#    - 持仓时间建议
#    - 仓位管理方案
#    - 对冲策略选择
#    - 调仓条件设定

#    ## 退出策略
#    - 止盈价位设置
#    - 止损价位设置
#    - 风险预警机制
#    - 退出时机选择

# 7. 风险监控
#    - 技术面监控：
#      * 价格突破位
#      * 成交量异常
#      * 期权异常
#    - 基本面监控：
#    - 事件风险：

# 8. 假设和计算过程
# 如果在报告中你使用了任何没有提供的数据，做出了任何假设，或者进行了任何计算，请在这里详细列出。

# 报告要求：
# 1. 重点突出差异化观点，特别是与市场共识的分歧
# 2. 所有预测和假设必须基于已有数据和合理推导
# 3. 价格目标和交易建议需要有具体数据支持
# 4. 风险分析需要定量和定性相结合
# 5. 建议要有明确的执行条件和时间节点
# 6. 符合生物医药行业特点，突出研发和商业化进展
# 7. 适应美股市场特点，考虑全球竞争格局
# 8. 数据来源要在引用数据或观点时注明
# 9. 如果使用假设，请在结论旁标注假设条件
# 10. 如果使用计算，请在结论旁标注计算过程
# 11. 请用markdown格式输出报告，符合对冲基金投资报告的格式和行文最高标准
# 12. 请仔细检查报告内容，确保没有遗漏任何重要信息，确保没有错误，确保没有幻觉，确保没有编造数据

# ## 重要：生成的报告中的数据要完全基于上面提供的内容，请仔细检查，不允许编造任何数据。如果你对提供的数据进行了计算，需要给出详细的计算过程。报告的准确性非常重要，因此不允许编造数据。
# """ 

class IntegratedReportPrompt:
    def format(self, symbol: str, report_date: str, latest_price: float, market_cap: float, technical_analysis: str, financial_analysis: str, options_analysis: str = None, catalyst_analysis: str = None) -> str:
        """Format prompt for generating integrated investment report"""
        return f"""# 投资分析报告生成指令

## 核心要求（严格遵循）
⚠️ 所有结论必须通过以下方式验证：
1. 数据标注 → [来源类型: 数值@时间] 例：[财报: 收入$1.2B@2023-Q4]
2. 计算展示 → 公式 + 参数 + 结果 例：PE估值 = 25(行业PE) × 1.5(溢价系数) = 37.5
3. 假设声明 → [假设条件] + [影响范围] 例：[假设新药获批] 可能提升目标价30%

# 分析对象
{symbol} ({latest_price} @ {report_date})
市值: {market_cap}

## 输入数据
```markdown
### 技术分析
{technical_analysis}

### 财务分析
{financial_analysis}

### 期权分析
{options_analysis if options_analysis else "无数据"}

### 催化剂分析
{catalyst_analysis if catalyst_analysis else "无数据"}
```

## 报告结构（必须严格遵循）
1. 评级与目标价 [!必须包含计算过程]
   ``` 
   [买入/持有/卖出] 评级
   Bull Case: [数值] (需展示管线成功率×市场潜力)
   Base Case: [数值] (需加权平均各因素)
   Bear Case: [数值] (需计算风险折现)
   ```

2. 关键论证 [!三点式结构]
   ``` 
   【论点】[不超过10字]
   - 证据链: [数据1] → [数据2] → [推论]
   - 差异点: 与市场共识的关键分歧
   - 数据支撑: 至少2个数据点标注
   ```

3. 风险对冲方案 [!具体可执行]
   ``` 
   if [技术面信号] then [操作]
   - 触发条件: [指标+阈值]
   - 执行参数: [仓位比例/时间窗口]
   - 对冲成本: [计算过程]
   ```

4. 数据看板 [!结构化呈现]
   | 类别       | 关键指标                 | 近期变化      |
   |------------|--------------------------|---------------|
   | 技术面     | 支撑位/阻力位           | [变化趋势]    |
   | 财务       | 现金消耗率               | [计算过程]    |
   | 期权       | IV异常值                 | [数据来源]    |
   | 催化剂     | 事件概率                 | [计算依据]    |

## 禁令（零容忍）
❌ 禁止任何未标注来源的数据引用
❌ 禁止使用"可能""大概"等模糊表述
❌ 禁止省略计算过程直接给出结论

## 验证清单（生成后逐项检查）
[ ] 每个论点都有完整证据链
[ ] 每个数值都有计算/来源标注
[ ] 风险方案包含具体触发条件
[ ] 数据看板包含最新变化信息

## 重要！数据可靠性验证
立即终止分析如果发现：
1. 关键数据缺失影响核心结论
2. 不同数据源存在矛盾未解决
3. 数据时间超过有效期限：
   - 财务数据 >12个月
   - 技术数据 >1个月
   - 期权数据 >7个交易日

## 输出规范
```markdown
# [股票代码] 投资分析报告
**报告日期**：{report_date}

### 1. 投资结论
{{基于多维度分析的整合结论}}

### 2. 关键验证逻辑
{{结构化展示核心论证过程}}

### 3. 执行框架
{{具体可操作的交易计划}}
```
请严格按照此模板生成详细和全面的最终报告"""
    
class OneStepReportGenerationPrompt:
    def format(self, symbol: str, report_date: str, latest_price: float, market_cap: float, technical_data: str, financial_data: str, options_data: str, catalyst_data: str) -> str: 
        """Format prompt for generating integrated investment report"""
        return f"""请基于以下多个维度的分析结果，生成一份完整的投资分析报告。报告应该全面但重点突出，语言专业但易于理解，分析深入且具有前瞻性，建议明确且可操作。
        请只使用提供的数据进行分析，并在使用时在结论旁标注数据来源（例如："根据财报数据，2024年收入为$100M，同比增长20%"，"根据电话会议记录，管理层预计2025年收入为$150M，同比增长15%"等）。所有假设和预测必须基于已有数据和合理推导，绝对绝对不要编造数据和幻觉，如果你需要的数据没有提供，请直接指出该部分数据缺失。如果你采用的数据是基于假设的，请在结论旁标注假设条件。

# 分析对象
{symbol}

# 报告日期
{report_date}

# 最新价格
{latest_price}

# 市值
{market_cap}

# 催化剂事件分析数据（如有）
{catalyst_data}

# 技术分析和新闻数据
{technical_data}

# 财务分析数据
{financial_data}

# 期权分析数据（如有）
{options_data}

请按照以下结构生成最终报告：

1. 投资评级与目标价
   - 明确给出投资评级（买入/增持/持有/减持/卖出）
     * 基于三种情景给出12个月目标价：
       * Bull Case：基于产品管线全部成功和市场份额最大化
       * Base Case：基于当前发展轨迹和市场预期
       * Bear Case：基于竞争加剧和研发不及预期

   - 详细说明目标价的计算依据
   - 列举3-5个最具说服力的投资理由
   - 与市场一致预期的差异分析

2. 执行摘要
   - 投资亮点：
     * 产品商业化进展
     * 研发管线里程碑
     * 市场份额变化
     * 财务指标改善
   - 最新催化剂事件分析：
     * 催化剂事件
     * 催化剂事件正向和负向发生的概率和支持该概率的证据
     * 催化剂事件对目标价的影响
   - 风险提示：
     * 研发风险
     * 竞争风险
     * 监管风险
     * 财务风险

3. 核心产品分析
   - 已上市产品：
     * 市场份额
     * 销售增长
     * 竞争态势
     * 专利保护
   - 在研产品：
     * 临床进展
     * 市场潜力
     * 成功概率
     * 预计上市时间
   - 催化剂事件：
     * 催化剂事件和对近期重要事件的详细分析，包括相关产品详细的临床数据（设计，有效性，安全性等），分析和比较
     * 催化剂事件正向和负向发生的概率和支持该概率的证据
     * 催化剂事件对目标价的影响

4. 财务分析
   - 收入结构：
     * 产品收入占比
     * 地区收入分布
     * 增长驱动因素
   - 盈利能力：
     * 毛利率趋势
     * 费用控制
     * 现金流状况
   - 研发投入：
     * 投入规模
     * 资金来源
     * 重点项目

5. 技术面分析
   - 趋势研判：
     * 主要趋势方向
     * 趋势强度
     * 关键支撑位
     * 重要阻力位
   - 成交量分析：
     * 量价配合
     * 机构动向
     * 异常交易
   - 市场情绪：
     * 技术指标
     * 期权市场反映
     * 机构持仓变化

6. 交易建议
   ## 建仓策略
   - 目标价位区间
   - 分批建仓比例
   - 建仓时机选择
   - 风险控制措施

   ## 持仓管理
   - 持仓时间建议
   - 仓位管理方案
   - 对冲策略选择
   - 调仓条件设定

   ## 退出策略
   - 止盈价位设置
   - 止损价位设置
   - 风险预警机制
   - 退出时机选择

7. 风险监控
   - 技术面监控：
     * 价格突破位
     * 成交量异常
     * 期权异常
   - 基本面监控：
   - 事件风险：

8. 假设和计算过程
如果在报告中你使用了任何没有提供的数据，做出了任何假设，或者进行了任何计算，请在这里详细列出。

报告要求：
1. 重点突出差异化观点，特别是与市场共识的分歧
2. 所有预测和假设必须基于已有数据和合理推导
3. 价格目标和交易建议需要有具体数据支持
4. 风险分析需要定量和定性相结合
5. 建议要有明确的执行条件和时间节点
6. 符合生物医药行业特点，突出研发和商业化进展
7. 适应美股市场特点，考虑全球竞争格局
8. 数据来源要在引用数据或观点时注明
9. 如果使用假设，请在结论旁标注假设条件
10. 如果使用计算，请在结论旁标注计算过程
11. 请用markdown格式输出报告，符合对冲基金投资报告的格式和行文最高标准
12. 请仔细检查报告内容，确保没有遗漏任何重要信息，确保没有错误，确保没有幻觉，确保没有编造数据
13. 请尽量详细和全面的输出结果

## 重要：生成的报告中的数据要完全基于上面提供的内容，请仔细检查，不允许编造任何数据。如果你对提供的数据进行了计算，需要给出详细的计算过程。报告的准确性非常重要，因此不允许编造数据。
""" 
    
class TwoStepReportGenerationPrompt:
    def format(self, symbol: str, report_date: str, latest_price: float, market_cap: float, technical_data: str, financial_data: str, options_data: str, catalyst_analysis: str) -> str: 
        """Format prompt for generating integrated investment report"""
        return f"""# 生物医药投资分析报告生成指令

## 核心规范（严格遵循）
⚠️ 采用「数据-推论-验证」三段式论证结构：
1. 数据锚点：必须标注 [来源类型: 数值@时间]
2. 推论链条：展示至少3个数据点的逻辑关系
3. 交叉验证：技术面与基本面结论必须相互印证

# 分析对象
{symbol} ({latest_price} @ {report_date})
市值: {market_cap}

## 输入数据集
```markdown
### 催化剂事件分析
{catalyst_analysis}

### 技术数据，新闻和公告以及社交媒体信息
{technical_data}

### 财务数据和投资者会议记录
{financial_data}

### 期权链
{options_data}
```

## 报告架构（按顺序生成）

1. 投资评级与目标价
   - 明确给出投资评级（买入/增持/持有/减持/卖出）
     * 基于三种情景（Bull Case, Base Case, Bear Case）给出12个月目标价和理由：
   - 详细说明目标价的计算依据
   - 列举3-5个最具说服力的投资或者做空理由

2. 详细催化剂事件分析：
   - 催化剂事件的具体描述
   - 相关药物的详细数据
   - 竞争对手的临床数据
   - 催化剂事件正向和负向发生的概率和支持该概率的证据
   - 催化剂事件对目标价的影响

3. 详细财务分析
   - 收入：
     * 产品/地区收入占比
     * 主要产品情况
     * 增长驱动因素
   - 盈利能力和现金流状况
   - 研发投入和重点项目

4. 详细技术面分析
   - 趋势研判：
     * 主要趋势方向
     * 趋势强度
     * 关键支撑位
     * 重要阻力位
   - 异常交易
      * 异常交易日期
      * 异常交易价格
      * 异常交易量
   - 结合新闻和公告，催化剂等信息，判断是否能够解释技术面分析
   - 通过技术面分析评估市场是否存在对未来催化剂事件提前price in

5. 详细期权分析
通过详尽的期权分析判断
   - 机构动向
   - 策略
   - 异常交易和潜在内幕消息，是否为对未来催化剂事件结果的提前泄露
   - 市场情绪，是否存在过度乐观或过度悲观
   - 结合新闻和公告，催化剂等信息，判断是否能够解释期权分析
   - 通过期权分析评估市场是否存在对未来催化剂事件提前price in

6. 可操作的交易建议
   ## 建仓策略（做空/做多/观望）
   - 目标价位区间
   - 分批建仓比例
   - 建仓时机选择
   - 风险控制措施

   ## 持仓管理
   - 持仓时间建议
   - 仓位管理方案
   - 对冲策略选择
   - 调仓条件设定

   ## 退出策略
   - 止盈价位设置
   - 止损价位设置
   - 风险预警机制
   - 退出时机选择

7. 风险监控
   - 技术面监控：
     * 价格突破位
     * 成交量异常
     * 期权异常
   - 基本面监控：
   - 事件风险：

8. 详细的假设和计算过程
如果在报告中你使用了任何没有提供的数据，做出了任何假设，或者进行了任何计算，请在这里详细列出。

报告要求：
1. 符合对冲基金投资报告的格式和行文最高标准
2. 所有预测和假设必须基于已有数据和合理推导
3. 价格目标和交易建议需要有具体数据支持
4. 建议要有明确的执行条件和时间节点
5. 符合生物医药行业特点，突出研发和商业化进展
6. 适应美股市场特点，考虑全球竞争格局
7. 数据来源要在引用数据或观点时注明
8. 请用markdown格式输出报告
9. 请仔细检查报告内容，确保所有报告中的数据都来自**输入数据集**，没有遗漏任何重要信息，确保没有错误，确保没有幻觉，确保没有编造数据
10. 请尽量详细和全面的输出结果
""" 

class SpecializedReportPrompt:
    def format(self, symbol: str, report_date: str, latest_price: float, market_cap: float, technical_data: str, financial_data: str, options_data: str, catalyst_analysis: str) -> str: 
        """Format prompt for generating integrated investment report"""
        return f"""# 生物医药投资分析报告生成指令

## 核心规范（严格遵循）
⚠️ 采用「数据-推论-验证」三段式论证结构：
1. 数据锚点：必须标注 [来源类型: 数值@时间]
   - 若新闻类型数据锚点有url链接则标注改为 [新闻@<时间>: [<Source>](<Url>)]
2. 推论链条：展示至少3个数据点的逻辑关系
3. 交叉验证：技术面与基本面结论必须相互印证

# 分析对象
{symbol} ({latest_price} @ {report_date})
市值: {market_cap}

## 输入数据集
```markdown
### 催化剂事件分析
{catalyst_analysis}

### 技术数据，新闻和公告以及社交媒体信息
{technical_data}

### 财务数据和投资者会议记录
{financial_data}

### 期权链
{options_data}
```

## 报告架构（按顺序生成）
1. 股价异动归因分析
   - 股价异动情况
   - 详细分析股价异动的原因
      * 重点参考新闻与公告，催化剂事件及临床试验数据

2. 投资评级与目标价
   - 明确给出投资评级（买入/增持/持有/减持/卖出）
     * 基于三种情景（Bull Case, Base Case, Bear Case）给出12个月目标价和理由：
   - 详细说明目标价的计算依据
   - 列举3-5个最具说服力的投资或者做空理由
   
3. 可操作的交易建议
   ## 建仓策略（做空/做多/观望）
   - 目标价位区间
   - 分批建仓比例
   - 建仓时机选择
   - 风险控制措施

   ## 持仓管理
   - 持仓时间建议
   - 仓位管理方案
   - 对冲策略选择
   - 调仓条件设定

   ## 退出策略
   - 止盈价位设置
   - 止损价位设置
   - 风险预警机制
   - 退出时机选择

4. 详细的假设和计算过程
如果在报告中你使用了任何没有提供的数据，做出了任何假设，或者进行了任何计算，请在这里详细列出。

报告要求：
1. 所有预测和假设必须基于已有数据和合理推导
2. 价格目标和交易建议需要有具体数据支持
4. 建议要有明确的执行条件和时间节点
5. 符合生物医药行业特点，突出研发和商业化进展
6. 适应美股市场特点，考虑全球竞争格局
7. 数据来源要在引用数据或观点时注明
8. 请用markdown格式输出报告
9. 请仔细检查报告内容，确保所有报告中的数据都来自**输入数据集**，没有遗漏任何重要信息，确保没有错误，确保没有幻觉，确保没有编造数据
10. 请尽量详细和全面的输出结果
""" 