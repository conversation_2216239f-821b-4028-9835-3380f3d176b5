from datetime import datetime
import io
import traceback
import pandas as pd
from typing import Dict, Any
import json
import pytz
from agent.synopsis.hallucination_checker import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from agent.synopsis.synopsis_query_trials import search_trials
from utils.bio_quant.dummy_stream import dummy_stream
from abc import ABC
import os
from agent.synopsis.prompts.report import full_prompt_template_cn
from agent.synopsis.prompts.report_en import full_prompt_template_en 
from agent.synopsis.prompts.output import synopsis_output_template, synopsis_output_template_order
from agent.synopsis.prompts.output_partial_en import synopsis_output_template_a_en, synopsis_output_template_b_en, synopsis_output_template_c_en, synopsis_output_template_d_en
from agent.synopsis.prompts.output_partial import synopsis_output_template_a, synopsis_output_template_b, synopsis_output_template_c, synopsis_output_template_d
from agent.synopsis.prompts.output_backup import synopsis_output_template_text
import re
import asyncio
from utils.sql_client import get_connection, text

class SynopsisAnalyzer(ABC):
    def __init__(self, model, query_params={}, **kwargs):
        """Initialize synopsis analyzer with LLM model"""
        self.synopsis = ''
        self.trial_data = {}
        self.model = model
        self.query_params = query_params
        self.chinese_query_params = False
        self.output_dir = './outputs'
        self.gemini_mode = False
        self.synopsis_parts = []
        self.language = kwargs.get('language', 'en')
        if self.language == 'cn':
            self.synopsis_template_parts = [synopsis_output_template_a, synopsis_output_template_b, synopsis_output_template_c, synopsis_output_template_d]
        else:
            self.synopsis_template_parts = [synopsis_output_template_a_en, synopsis_output_template_b_en, synopsis_output_template_c_en, synopsis_output_template_d_en]

    def set_output_dir(self, output_dir: str):
        """Set output directory for synopsis results"""
        self.output_dir = output_dir

    async def synopsis_gen_stream(self, test=False, stream_status={}):
        """Analyze synopsis data and return insights"""
        try:
            trial_data = self.trial_data
            for i in range(len(trial_data),0,-1):
                    print(f"len(str(trials[:{i}]))", len(str(trial_data[:i])))
                    if len(str(trial_data[:i])) < 120000:
                        trial_data = trial_data[:i]
                        break
            data_dir = os.path.join(self.output_dir, "data")
            os.makedirs(data_dir, exist_ok=True)
            trial_data_file = os.path.join(data_dir, 'synopsis_trial_data_selected.json')
            with open(trial_data_file, 'w', encoding='utf-8') as f:
                json.dump(trial_data, f, indent=4, ensure_ascii=False)
            print(f"- 临床实验数据已保存至：{trial_data_file}")
            
            
            trial_data_str = json.dumps(trial_data, ensure_ascii=False, separators=(',', ':'))
            print("len(trial_data_str)", len(trial_data_str))
            
            self.prompt_template = full_prompt_template_cn if self.language == 'cn' else full_prompt_template_en
            self.hallucination_checker = HallucinationChecker(language=self.language)

            # if only_return_data:
            #     return trial_data_str
            
            print("- 生成分析提示")
            # Generate prompt
            # prompt_file = os.path.join(data_dir, 'synopsis_output_template.txt')
            # with open(prompt_file, 'w', encoding='utf-8') as f:
            #     f.write(synopsis_output_template)
            # print(f"- 输出模版已保存至：{prompt_file}")
            
            # prompt_file = os.path.join(data_dir, 'prompt_template.txt')
            # with open(prompt_file, 'w', encoding='utf-8') as f:
            #     f.write(self.prompt_template)
            # print(f"- 提示词模版已保存至：{prompt_file}")
   
            prompt = self.prompt_template.format(
                query_params=self.query_params,
                current_date=datetime.now(pytz.timezone('US/Eastern')).strftime('%Y-%m-%d'),
                trial_data=trial_data_str,
                synopsis_output_template=synopsis_output_template_text if self.gemini_mode else synopsis_output_template
            )
            
            # Save prompt to file
            prompt_dir = os.path.join(self.output_dir, "prompts")
            os.makedirs(prompt_dir, exist_ok=True)
            prompt_file = os.path.join(prompt_dir, 'synopsis_draft_prompt.txt')
            with open(prompt_file, 'w', encoding='utf-8') as f:
                f.write(prompt)
            print(f"- 提示词已保存至：{prompt_file}")
            
            self.prompt = prompt
            
            stream_status['trial_data_obtained'] = True
            print("- 调用 AI 模型进行分析")
            # Get synopsis from LLM
            synopsis_gen = self.model.generate_stream(prompt, stream=True, stream_status=stream_status, system_prompt=False)
            string_buffer = io.StringIO()
            async for chunk in synopsis_gen:
                if chunk:
                    string_buffer.write(chunk)
                    yield chunk
            draft = string_buffer.getvalue()
            string_buffer.close()
            self.draft = draft.replace('```markdown', '').replace('```', '')
            
        except Exception as e:
            print(f"\n错误详情：")
            import traceback
            print(traceback.format_exc())
            raise Exception(str(e))
        
    async def check_hallucination(self, test=False, **kwargs):
        try:
            # hallucination checking
            verified_synopsis = dummy_stream("check_hallucination", stream_status=kwargs["stream_status"]) if test else self.hallucination_checker.check_and_save_stream(
                model=self.model,
                prompt=self.prompt,
                response=self.draft,
                analysis_type="synopsis",
                output_dir=self.output_dir
            ) 
            
            async for chunk in verified_synopsis:
                if test:
                    await asyncio.sleep(0.1)
                yield chunk

            verified_result = getattr(self.hallucination_checker, "verified_response", None)
            if not verified_result:
                print("综述幻觉检查失败")
                return
            
            # Extract verified synopsis from verified_synopsis, tagged by <synopsis>...</synopsis>
            verified_synopsis_search = re.search(r'<synopsis>(.*?)</synopsis>', verified_result, re.DOTALL)
            self.synopsis = verified_synopsis_search.group(1) if verified_synopsis_search else verified_result
            if not self.synopsis:
                raise Exception("综述幻觉检查失败, 未能匹配<synopsis>")
            
            data_dir = os.path.join(self.output_dir, "data")
            synopsis_file = os.path.join(data_dir, 'synopsis.md')
            with open(synopsis_file, 'w', encoding='utf-8') as f:
                f.write(self.synopsis)
            print(f"- 综述已保存至：{synopsis_file}")
            
            full_synopsis_file = os.path.join(data_dir, 'synopsis_full.md')
            with open(full_synopsis_file, 'w', encoding='utf-8') as f:
                f.write(verified_result)
            print(f"- 综述已保存至：{full_synopsis_file}")            
            
            
            print("- 综述幻觉检查完成")
        except Exception as e:
            traceback.print_exc()
            raise Exception(str(e))
            
            
    def run_search_trials(self):
        trials, other_params = search_trials(**self.query_params)
        self.other_params = list(other_params.keys())
        # self.cleanup_trial_data(trials)
        if not trials:
            raise Exception("未找到符合条件的临床实验数据")
        trial_ids = [trial['id'] for trial in trials]

        sql = f" SELECT nct_id, efficacy, safety, design FROM biomedtracker_trial_workflow WHERE nct_id IN :trial_ids"
        params = {'trial_ids': tuple(trial_ids)}
        with get_connection() as conn:
            result = conn.execute(text(sql), params)
            data = result.fetchall()
        result_dict = {}
        for row in data:
            result_dict[row[0]] = {'efficacy': row[1], 'safety': row[2], 'design': row[3]}
        for trial in trials:
            if trial['id'] in result_dict:
                trial['efficacy'] = result_dict[trial['id']]['efficacy']
                trial['safety'] = result_dict[trial['id']]['safety']
                trial['design'] = result_dict[trial['id']]['design']
        data_dir = os.path.join(self.output_dir, "data")
        os.makedirs(data_dir, exist_ok=True)
        trial_data_file = os.path.join(data_dir, 'synopsis_trial_data.json')
        with open(trial_data_file, 'w', encoding='utf-8') as f:
            json.dump(trials, f, indent=4, ensure_ascii=False)
        print(f"- 临床实验数据(all)已保存至：{trial_data_file}")
        self.trial_data = trials
        
    def save_trial_data(self):
        data_dir = os.path.join(self.output_dir, "data")
        os.makedirs(data_dir, exist_ok=True)
        trial_data_file = os.path.join(data_dir, 'synopsis_trial_data.json')
        with open(trial_data_file, 'w', encoding='utf-8') as f:
            json.dump(self.trial_data, f, indent=4, ensure_ascii=False)
        print(f"- 临床实验数据已保存至：{trial_data_file}")
        
    # def cleanup_trial_data(self, trials):
    #     for r in trials:
    #         for key in list(r.keys()):
    #             if key not in ['protocolSection', 'id']:
    #                 r.pop(key, "")
    #         protocolSection = r['protocolSection']
    #         for key in list(protocolSection.keys()):
    #             if key not in ['identificationModule', 'designModule', 'eligibilityModule', 
    #                     'statusModule', 'outcomesModule', 'armsInterventionsModule', 
    #                     'sponsorCollaboratorsModule', 'conditionsModule','contactsLocationsModule', 'description']:
    #                 protocolSection.pop(key, "")
    #             if key == 'identificationModule':
    #                 for key in list(protocolSection['identificationModule'].keys()):
    #                     if key not in ['nctId', 'organization', 'briefTitle', 'officialTitle']:
    #                         protocolSection['identificationModule'].pop(key, "")
    #             if key == 'contactsLocationsModule':
    #                 for key in list(protocolSection['contactsLocationsModule'].keys()):
    #                     if key not in ['location']:
    #                         protocolSection['contactsLocationsModule'].pop(key, "")
# if __name__ == "__main__":   
#     ca = SynopsisAnalyzer(VolcanoDeepSeekModel(test_mode=True), data_source=None)
#     ca.set_output_dir('./outputs')
#     # async def test():
#     #     res = await ca.analyze({'symbol':'OMER'})
#     #     print("res", res)
#     # asyncio.run(test())
        
#     async def test():
#         result_gen = ca.synopsis_gen_stream({'symbol':'OMER'})
#         async for result in result_gen:
#             print(result)
                
#     asyncio.run(test())