from typing import List, Optional, Type
from pydantic import BaseModel, Field

from agent.synopsis.enums import *

class SynopsisTranslationSchema(BaseModel):
    translated_indication: str = Field(description="English indication name (must be in english, translate into english if not)")
    # phase: List[str] = Field(description="Phase", json_schema_extra={"optional": True}, examples=phases)
    # treatment_line: List[str] = Field(description="Treatment line", json_schema_extra={"optional": True}, examples=treatment_lines)
    # health_condition: List[str] = Field(description="Health condition", json_schema_extra={"optional": True}, examples=health_conditions)
    # sex: Optional[str] = Field(description="Patient sex", json_schema_extra={"optional": True}, examples=sex)
    # age: Optional[str] = Field(description="Patient age", json_schema_extra={"optional": True}, examples=ages)
    # intervention_model: List[str] = Field(description="Intervention models", json_schema_extra={"optional": True}, examples=intervention_models)
    # masking: List[str] = Field(description="Masking type", json_schema_extra={"optional": True}, examples=masking_types)
