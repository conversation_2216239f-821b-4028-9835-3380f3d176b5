full_prompt_template_cn = """
<试验方案摘要规范>
{query_params}
</试验方案摘要规范>
请根据<试验方案摘要规范>中提供的目标参数，参考并模仿<Noah数据>中的试验数据，生成一份临床试验方案摘要，并按<输出模板>格式输出。

<Noah数据>
{trial_data}
</Noah数据>

生成日期：{current_date}

## 摘要结构（按顺序生成）
<输出模板>
{synopsis_output_template}
</输出模板>

"""

partial_prompt_template_cn = """
<试验方案摘要规范>
{query_params}
</试验方案摘要规范>

生成日期：{current_date}

用户希望生成一份全新的、原创的临床试验方案摘要。我们将逐节编写。
请仔细研究<Noah数据>中符合<试验方案摘要规范>要求的临床试验数据（不包括{other_params}的要求），并根据<试验方案摘要规范>中定义的参数，按照<输出模板>中指定的格式生成临床试验方案摘要部分。

<Noah数据>
{trial_data}
</Noah数据>

## 摘要结构（按顺序生成）
<输出模板>
{synopsis_output_template}
</输出模板>

要求：
1. 请为每个部分提供全面、详细的内容
{extra_requirements}
"""

partial_prompt_template_post_cn = """
<试验方案摘要规范>
{query_params}
</试验方案摘要规范>

生成日期：{current_date}

用户希望生成一份全新的、原创的临床试验方案摘要。我们将逐节编写。
基于我们在<进行中的摘要>中已经编写的部分，生成<输出模板>中指定的临床试验方案摘要部分。

<进行中的摘要>
{synopsis_parts}
</进行中的摘要>

## 摘要结构（按顺序生成）
<输出模板>
{synopsis_output_template}
</输出模板>

要求：
1. 请为每个部分提供全面、详细的内容
{extra_requirements}
"""

partial_prompt_template_chain_cn = """
<试验方案摘要规范>
{query_params}
</试验方案摘要规范>

<进行中的摘要>
{synopsis_parts}
</进行中的摘要>

生成日期：{current_date}

用户希望生成一份全新的、原创的临床试验方案摘要。我们将逐节编写。
请仔细研究<Noah数据>中符合<试验方案摘要规范>要求的临床试验数据（不包括{other_params}的要求），并根据<试验方案摘要规范>中定义的参数，按照<输出模板>中指定的格式生成临床试验方案摘要部分。
参考<进行中的摘要>中我们已经编写的部分，确保一致性和连贯性。

<Noah数据>
{trial_data}
</Noah数据>

## 摘要结构（按顺序生成）
<输出模板>
{synopsis_output_template}
</输出模板>

要求：
1. 请为每个部分提供全面、详细的内容
{extra_requirements}
"""

merge_prompt_cn = """
<试验方案摘要规范>
{query_params}
</试验方案摘要规范>

您需要将<摘要片段>中的内容合并成各个部分，同时保留所有原始文本和详细信息，按照<输出顺序>中指定的顺序输出临床试验方案摘要。

<摘要片段>
{synopsis_parts}
</摘要片段>

生成日期：{current_date}

## 摘要顺序
<输出顺序>
{synopsis_output_template}
</输出顺序>

要求：
1. 合并各部分时，保留<摘要片段>中每个部分的所有文本和详细信息，不要简化、概括或省略任何内容。
2. 在不省略每个片段中其他部分文本的同时，合并其附录部分的术语表和参考文献。
3. 确保每个部分都包含<摘要片段>中每个片段的完整文本
"""