full_prompt_template_en = """
<Synopsis Specification>
{query_params}
</Synopsis Specification>
Please generate a clinical trial synopsis based on the target parameters provided in <Synopsis Specification> by referencing and imitating trial data from <Noah Data> and output it in the provided format <Output Template>.

<Noah Data>
{trial_data}
</Noah Data>

Generated on: {current_date}

## Synopsis Structure (Generate in Order)
<Output Template>
{synopsis_output_template}
</Output Template>

"""

partial_prompt_template_en = """
<Synopsis Specification>
{query_params}
</Synopsis Specification>

Generation Date: {current_date}

The user wants to generate a new, original clinical trial synopsis. We will write it section by section.
Please thoroughly study the clinical trial data provided in <Noah Data> that meets the <Synopsis Specification> requirements (excluding requirements for {other_params}), and based on the parameters defined in <Synopsis Specification>, generate the clinical trial synopsis section in the format specified in <Output Template>.

<Noah Data>
{trial_data}
</Noah Data>

## Synopsis Structure (generate in order)
<Output Template>
{synopsis_output_template}
</Output Template>

Requirements:
1. Please provide comprehensive, detailed content for each section
{extra_requirements}
"""

partial_prompt_template_post_en = """
<Synopsis Specification>
{query_params}
</Synopsis Specification>

Generation Date: {current_date}

The user wants to generate a new, original clinical trial synopsis. We will write it section by section.
Based on the sections we have already written in <Synopsis In Progress>, generate the clinical trial synopsis section specified in <Output Template>.

<Synopsis In Progress>
{synopsis_parts}
</Synopsis In Progress>

## Synopsis Structure (generate in order)
<Output Template>
{synopsis_output_template}
</Output Template>

Requirements:
1. Please provide comprehensive, detailed content for each section
{extra_requirements}
"""

partial_prompt_template_chain_en = """
<Synopsis Specification>
{query_params}
</Synopsis Specification>

<Synopsis In Progress>
{synopsis_parts}
</Synopsis In Progress>

Generation Date: {current_date}

The user wants to generate a new, original clinical trial synopsis. We will write it section by section.
Please thoroughly study the clinical trial data provided in <Noah Data> that meets the <Synopsis Specification> requirements (excluding requirements for {other_params}), and based on the parameters defined in <Synopsis Specification>, generate the clinical trial synopsis section in the format specified in <Output Template>.
Refer to the sections we have already written in <Synopsis In Progress> for consistency and coherence.

<Noah Data>
{trial_data}
</Noah Data>

## Synopsis Structure (generate in order)
<Output Template>
{synopsis_output_template}
</Output Template>

Requirements:
1. Please provide comprehensive, detailed content for each section
{extra_requirements}
"""

merge_prompt_en = """
<Synopsis Specification>
{query_params}
</Synopsis Specification>

You need to combine the <Synopsis Chunks> into sections while preserving all original text and details, outputting the clinical trial synopsis according to the order specified in <Output Order>.

<Synopsis Chunks>
{synopsis_parts}
</Synopsis Chunks>

Generation Date: {current_date}

## Synopsis Order
<Output Order>
{synopsis_output_template}
</Output Order>

Requirements:
1. When combining sections, preserve all text and details of each section without simplifying, summarizing, or omitting any content from <Synopsis Chunks>. 
2. While not omitting any text from the other sections in each chunk, merge their Appendices sections' glossary of terms and references.
3. Ensure that each section has the full text from each chunk in <Synopsis Chunks>
"""