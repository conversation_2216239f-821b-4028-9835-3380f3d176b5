from datetime import datetime
import json
import shutil
import os
import asyncio
import io
import logging
import time
import traceback
from typing import Any, Callable, List, Type

from agent.core.preset import AgentPreset
from llm.azure_models import GPT4o
from llm.base_model import BaseLLM
from agent.explore.schema import ProcessingType, SearchNode, SearchType, WebSearchLink, WebSearchSubject
from agent.explore.helper import MindSearchHelper
from agent.synopsis.synopsis_analyzer import SynopsisAnalyzer
from utils.core.exception import UnexpectedException

logger = logging.getLogger(__name__)

class SynopsisAgent(AgentPreset):
    llm: BaseLLM = GPT4o
    sys_prompt: str = ""
    mindsearch_helper: MindSearchHelper = MindSearchHelper()
    language: str = "cn"
    test: bool = False
    synopsis_analyzer: SynopsisAnalyzer = None
    analyzer_class: Type[SynopsisAnalyzer] = SynopsisAnalyzer
    query_mode: bool = False
    oneshot: bool = False
    
    def __init__(self, query_params, query_mode=False, oneshot=False, gemini_mode=False, **kwargs):
        from bio_quant.models.composite_model import CompositeModel
        from llm.gcp_models import Gemini20Flash, Gemini15Flash
        super().__init__()
        try:
            if 'params' in kwargs and 'language' in kwargs['params']:
                lang = kwargs['params']['language'].lower()
                if lang in ['cn', 'zh']:
                    lang = 'cn'
                self.language = lang
            if 'language' in query_params:
                self.language = query_params.pop('language', 'en').lower()
        except: pass
        if 'test' in kwargs and type(kwargs['test']) == bool:
            self.test = kwargs.pop('test',False)
  
        output_dir = f"outputs/synopsis_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.query_mode = query_mode
        self.oneshot = oneshot
        
        self.synopsis_analyzer = self.analyzer_class(model=Gemini20Flash() if gemini_mode else CompositeModel(), query_params=query_params, language=self.language)
        self.synopsis_analyzer.set_output_dir(output_dir)
        self.synopsis_analyzer.gemini_mode = gemini_mode

    async def run_func(self, func: Callable, buffer: io.StringIO):
        async_generator = func()
        try:
            async for item in async_generator:
                buffer.write(item)
        except Exception as e:
            logger.error(f"run_func failed: {str(e)}")
            raise e
                
    async def use_tool(self, user_prompt: str = "", **kwargs):
        from utils.obs.client import upload_file
        try:
            if self.query_mode:
                # Immediately send an empty body for frontend to show user question
                response = self.mindsearch_helper.init_response(self)
                yield
                
                response.search_graph = self.init_search_graph(query_mode=True)
                yield response
                sa = self.synopsis_analyzer
                try:
                    sa.run_search_trials()
                except Exception as e:
                    response.content = str(e)
                    response.search_graph.summary = "DONE"
                    yield response
                    return
                yield response
                sa.save_trial_data()
                bucket_name = "noahai-userdata-test"
                user = kwargs.get("user", "unknown")
                file_name = f"{sa.output_dir}.json"
                file_path = os.path.join(sa.output_dir, 'data', 'synopsis_trial_data.json')
                for _ in range(3):
                    res = upload_file(bucket_name=bucket_name, object_key=f"synopsis/{user}/{file_name}", file_path=file_path)
                    if res: 
                        logger.info(f"File {file_path} uploaded successfully")
                        response.search_graph.attachments_key = f"synopsis/{user}/{file_name}"
                        break
                    await asyncio.sleep(3)
                else:
                    logger.error(f"Failed to upload {file_path}")
                response.content = "## 下载链接：[临床实验数据]" if self.language == 'cn' else "## Download link: [Trial Data]"
                response.content += f"(https://{bucket_name}.obs.cn-south-1.myhuaweicloud.com/{response.search_graph.attachments_key})" if response.search_graph.attachments_key else ""
                response.search_graph.summary = "DONE"
                yield response
                return
            # check whether need query
            start_time = time.time()
            # query rewrite
            response = self.mindsearch_helper.init_response(self)
            yield response
            response.search_graph = self.init_search_graph()
            yield response
            
            sa = self.synopsis_analyzer
            sa.run_search_trials()
            
            response.search_graph.children[0].summary = "DONE"
            yield response
            
            logger.info(f"Mindesearch final response input: {kwargs}")
            stream_status = {"enabled": True}
            buffer = io.StringIO()
            buffer.seek(0)
            buffer.truncate(0)
            stream_status = {"enabled": True}
            async for _ in self._task_with_heartbeat(sa.synopsis_gen_stream, buffer=buffer, stream_status=stream_status):
                s = buffer.getvalue()
                response.search_graph.children[1].thought_process = s
                response.content = s
                yield response  
            response.search_graph.children[1].summary = "DONE"
            response.content += "\n\n\n"
            response.content += "## 综述下载链接将在校对后提供，请稍候" if self.language == "cn" else "## Download link: will be ready after proofreading. Please wait for a moment."
            buffer.seek(0)
            buffer.truncate(0)
            
            # Set socket status as final responsing
            response.processing_type = ProcessingType.RESPONSING
            stream_status = {"enabled": True}
            async for _ in self._task_with_heartbeat(sa.check_hallucination, buffer=buffer, interval=1, stream_status=stream_status):
                response.search_graph.children[2].thought_process = buffer.getvalue()
                yield response
            buffer.close()
            response.search_graph.children[2].summary = "DONE"
            yield response
            
            logger.info(f"MindSearch final output: time passed {time.time() - start_time}s")
            
            # Save report and outputs to zip file
            zip_path = f"{sa.output_dir}.zip"
            if not os.path.exists(sa.output_dir + '/data'):
                os.makedirs(sa.output_dir + '/data', exist_ok=True)
            shutil.make_archive(sa.output_dir, 'zip', sa.output_dir + '/data')
            logger.info(f"Output saved to {zip_path}")
            
            bucket_name = "noahai-userdata-test"
            user = kwargs.get("user", "unknown")
            file_name = sa.output_dir + ".zip"
            for _ in range(3):
                res = upload_file(bucket_name=bucket_name, object_key=f"synopsis/{user}/{file_name}", file_path=zip_path)
                if res: 
                    logger.info(f"File {zip_path} uploaded successfully")
                    response.search_graph.attachments_key = f"synopsis/{user}/{file_name}"
                    break
                await asyncio.sleep(3)
            else:
                logger.error(f"Failed to upload {zip_path}")
                
            if hasattr(sa, "synopsis") and sa.synopsis:
                response.content = sa.synopsis
                response.content += "\n---\n\n"
                response.content += ("## 下载链接：[综述与数据]" if self.language == 'cn' else "## Download link: [Synopsis & Data]") + f"(https://{bucket_name}.obs.cn-south-1.myhuaweicloud.com/{response.search_graph.attachments_key})"
            yield response
            
            response.search_graph.summary = "DONE"
            yield response
            
        except Exception as e:
            traceback.print_exc()
            raise UnexpectedException(str(e))
        
    def init_search_graph(self, query_mode=False):
        root = SearchNode(search_type=SearchType.UNKNOWN,
                    query="Synopsis generation",
                    key_word="")
        subject = WebSearchSubject.UNKNOWN.value
        root.subject = WebSearchSubject(subject)
        if query_mode:
            return root
        root.thought_process = "报告生成将经过三个步骤" if self.language == 'cn' else "Synopsis generation follows a 3-step process"
        
        steps = ["Obtain clinical data (~5s)",
                "Synopsis generation (2-4 mins)",
                "Proofread synopsis (2-4 mins)"]
        steps_chinese = ["获取临床数据 (~5s)",
                        "综述生成 (2-4分钟)",
                        "综述校对 (2-4分钟)"]
        
        for subtitle in (steps_chinese if self.language == "cn" else steps):
            
            node = SearchNode(search_type=SearchType.UNKNOWN,
                    query=subtitle,
                    key_word="")
            root.add_child(node)
        
        return root
    
    async def _task_with_heartbeat(self, func: Callable, buffer: io.StringIO = None, interval: float = 0.3, stream_status={}, **kwargs):
        r"""
        Since fetch web page contents may cost very long time. Send heartbeat at the same time to avoid connection close.
        """
        try:
            start_time = time.time()
            async def write_buffer():
                f = func(test=self.test, stream_status=stream_status, **kwargs)
                if asyncio.iscoroutine(f):
                    await f
                    return
                async for item in f:
                    if not buffer or not item:
                        continue
                    buffer.write(item)
            task = asyncio.create_task(write_buffer())
            shielded = asyncio.shield(task)

            while not task.done():
                yield None
                await asyncio.sleep(interval)
            
            await shielded
            end_time = time.time()
            logger.info(f"[_task_with_heartbeat]{callable} cost time total {end_time - start_time}s")
            yield None
        except Exception as e:
            traceback.print_exc()
            raise Exception(f"Task {func.__name__} with heartbeat failed: {str(e)}")