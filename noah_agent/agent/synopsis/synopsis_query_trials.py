import os
import re
import elasticsearch
from dotenv import load_dotenv
from collections import Counter, defaultdict
import sqlalchemy as sa
import sqlalchemy.orm as orm
from sqlalchemy.ext.automap import automap_base, generate_relationship
load_dotenv()


def inclusion_criteria_numbers(criteria_string):
    inclusion_criteria_match = re.search(
        'Inclusion Criteria:(.+?)(Exclusion Criteria:|$)',
        criteria_string, re.DOTALL | re.IGNORECASE
    )

    if inclusion_criteria_match:
        line_match = re.compile(r'\*\s.+')
        inclusion_criteria_lines = [
            a.strip()
            for a in inclusion_criteria_match.group(1).split('\n')
            if line_match.match(a.strip())
        ]
        return len(inclusion_criteria_lines)
    else:
        return 0


def indication_query(indication):
    query_dict = {
        'bool': {
            'must': [
                {
                    'match': {
                        'protocolSection.conditionsModule.conditions': {
                            'query': indication,
                            'operator': 'AND'
                        }
                    }
                }
            ],
        }
    }
    return query_dict


def treatment_line_query(line):
    '''
    value of line should be one of: first-line, second-line, later-line, rr
    - first-line：First-Line Treatment, Newly Diagnosed, Treatment-Naïve, Initial Therapy, De Novo
    - second-line：Second-Line Treatment, Post First-Line Therapy
    - later-line：Later-Line Treatment, Advanced, Multiple Prior Lines of Therapy
    - rr：Relapsed/Refractory (R/R), Recurrent, Progressive Disease
    '''
    line_values = {
        'first-line': [
            'First-Line Treatment', 'Newly Diagnosed', 'Treatment-Naïve', 'Initial Therapy', 'De Novo'
        ],
        'second-line': [
            'Second-Line Treatment', 'Post First-Line Therapy'
        ],
        'later-line': [
            'Later-Line Treatment', 'Advanced', 'Multiple Prior Lines of Therapy'
        ],
        'rr': [
            'Relapsed/Refractory', '(R/R)', 'Recurrent', 'Progressive Disease'
        ]
    }
    if line not in line_values:
        raise ValueError(
            'line should be one of: [{}]'.format(', '.join(line_values.keys()))
        )
    else:
        pass
    query_dict = {
        'bool': {
            'should': []
        }
    }
    for lv in line_values[line]:
        query_dict['bool']['should'].append({
            'match': {
                'protocolSection.identificationModule.officialTitle': {
                    'query': lv,
                    'operator': 'AND',
                    'boost': 0.4
                }
            }
        })
    return query_dict


def health_condition_query(condition):
    '''
    value of conditon: Healthy Volunteers, HIV-positive, Diabetic, Hypertensive, Obese, Hepatic/Renal Impairment, Liver/Kidney Dysfunction
    '''
        # 'Healthy Volunteers', 'HIV-positive', 'Diabetic',
        # 'Hypertensive', 'Obese', 'Hepatic/Renal Impairment', 'Liver/Kidney Dysfunction'
    valid_conditions = {
        'healthy volunteers': 'Healthy Volunteers',
        'hiv-positive': 'HIV-positive',
        'diabetic': 'Diabetic',
        'hypertensive': 'Hypertensive',
        'obese': 'Obese',
        'hepatic/renal impairment': 'Hepatic/Renal Impairment', 
        'liver/kidney dysfunction': 'Liver/Kidney Dysfunction'
    }
    
    if condition not in valid_conditions:
        raise ValueError(
            'condition should be one of: [{}]'.format(', '.join(valid_conditions.values()))
        )
    else:
        pass
    query_dict = {
        'bool': {
            'should': [
                {
                    'match': {
                        'protocolSection.identificationModule.officialTitle': {
                            'query': valid_conditions[condition],
                            'operator': 'AND',
                            'boost': 0.3
                        }
                    }
                }
            ]
        }
    }
    return query_dict


def age_query(age):
    '''
    age should be one of: [CHILD, ADULT, OLDER_ADULT]
    '''
    valid_ages = ['CHILD', 'ADULT', 'OLDER_ADULT']
    if age not in valid_ages:
        raise ValueError(
            'age should be one of: [{}]'.format(', '.join(valid_ages))
        )
    else:
        pass
    query_dict = {
        'bool': {
            'must': [
                {
                    'match': {
                        'protocolSection.eligibilityModule.stdAges': {
                            'query': age
                        }
                    }
                }
            ],
        }
    }

    return query_dict


def only_age_query(age):
    '''
    age should be one of: [CHILD, ADULT, OLDER_ADULT]

    return the docs with only the age group
    '''
    valid_ages = ['CHILD', 'ADULT', 'OLDER_ADULT']
    if age not in valid_ages:
        raise ValueError(
            'age should be one of: [{}]'.format(', '.join(valid_ages))
        )
    else:
        pass
    must_not = ' '.join(set(valid_ages) - set([age]))
    query_dict = {
        'bool': {
            'must': [
                {
                    'match': {
                        'protocolSection.eligibilityModule.stdAges': {
                            'query': age
                        }
                    }
                }
            ],
            'must_not': [
                {
                    'match': {
                        'protocolSection.eligibilityModule.stdAges': {
                            'query': must_not,
                            'operator': 'OR'
                        }
                    }
                }
            ]
        }
    }

    return query_dict


def sex_query(sex):
    '''
    sex should be one of: [FEMALE, MALE, ALL]
    '''
    valid_sex = ['FEMALE', 'MALE', 'ALL']
    if sex in ['BOTH']:
        sex = 'ALL'
    if sex not in valid_sex:
        raise ValueError(
            'sex should be one of: [{}]'.format(', '.join(valid_sex))
        )
    else:
        pass
    query_dict = {
        'bool': {
            'should': [
                {
                    'match': {
                        'protocolSection.eligibilityModule.sex': {
                            'query': sex,
                            'boost': 0.3
                        }
                    }
                }
            ],
        }
    }
    return query_dict


def phase_query(phase):
    '''
    - 1: PHASE1
    - 1/2: PHASE1 & PHASE2
    - 2: PHASE2
    - 2/3: PHASE2 & PHASE3
    - 3: PHASE3
    - 4: PHASE4
    '''
    if phase == '1':
        query_dict = {
            'bool': {
                'must': [
                    {
                        'match': {
                            'protocolSection.designModule.phases': {
                                'query': 'PHASE1'
                            }
                        }
                    }
                ],
                'must_not': [
                    {
                        'match': {
                            'protocolSection.designModule.phases': {
                                'query': 'PHASE2'
                            }
                        }
                    }
                ]
            }
        }
    elif phase == '1/2':
        query_dict = {
            'bool': {
                'must': [
                    {
                        'match': {
                            'protocolSection.designModule.phases': {
                                'query': 'PHASE1 PHASE2',
                                'operator': 'AND'
                            }
                        }
                    }
                ],
            }
        }
    elif phase == '2':
        query_dict = {
            'bool': {
                'must': [
                    {
                        'match': {
                            'protocolSection.designModule.phases': {
                                'query': 'PHASE2'
                            }
                        }
                    }
                ],
                'must_not': [
                    {
                        'match': {
                            'protocolSection.designModule.phases': {
                                'query': 'PHASE1 PHASE3',
                                'operator': 'OR'
                            }
                        },
                    }
                ]
            }
        }
    elif phase == '2/3':
        query_dict = {
            'bool': {
                'must': [
                    {
                        'match': {
                            'protocolSection.designModule.phases': {
                                'query': 'PHASE2 PHASE3',
                                'operator': 'AND'
                            }
                        }
                    }
                ],
            }
        }
    elif phase == '3':
        query_dict = {
            'bool': {
                'must': [
                    {
                        'match': {
                            'protocolSection.designModule.phases': {
                                'query': 'PHASE3'
                            }
                        }
                    }
                ],
                'must_not': [
                    {
                        'match': {
                            'protocolSection.designModule.phases': {
                                'query': 'PHASE2'
                            }
                        }
                    }
                ]
            }
        }
    elif phase == '4':
        query_dict = {
            'bool': {
                'must': [
                    {
                        'match': {
                            'protocolSection.designModule.phases': {
                                'query': 'PHASE4'
                            }
                        }
                    }
                ],
            }
        }
    else:
        raise ValueError('phase should be one of: [1, 1/2, 2, 2/3, 3, 4]')
    return query_dict


def masking_query(masking):
    '''
    masking should be one of: [NONE, SINGLE, DOUBLE, TRIPLE, QUADRUPLE]
    - NONE - None (Open Label)
    - SINGLE - Single
    - DOUBLE - Double
    - TRIPLE - Triple
    - QUADRUPLE - Quadruple
    '''
    valid_masking = ['NONE', 'SINGLE', 'DOUBLE', 'TRIPLE', 'QUADRUPLE']
    if masking not in valid_masking:
        raise ValueError(
            'masking should be one of: [{}]'.format(', '.join(valid_masking))
        )
    else:
        pass
    query_dict = {
        'bool': {
            'should': [
                {
                    'match': {
                        'protocolSection.designModule.designInfo.maskingInfo.masking': {
                            'query': masking,
                            'boost': 0.2
                            # 'operator': 'AND'
                        }
                    }
                }
            ]
        }
    }
    return query_dict


def intervention_model_query(intervention):
    '''
    intervention should be one of: [SINGLE_GROUP, PARALLEL, CROSSOVER, FACTORIAL, SEQUENTIAL]
    - SINGLE_GROUP - Single Group Assignment
    - PARALLEL - Parallel Assignment
    - CROSSOVER - Crossover Assignment
    - FACTORIAL - Factorial Assignment
    - SEQUENTIAL - Sequential Assignment
    '''
    valid_intervention = [
        'SINGLE_GROUP', 'PARALLEL', 'CROSSOVER', 'FACTORIAL', 'SEQUENTIAL'
    ]
    if intervention not in valid_intervention:
        raise ValueError(
            'intervention should be one of: [{}]'.format(
                ', '.join(valid_intervention)
            )
        )
    else:
        pass
    query_dict = {
        'bool': {
            'should': [
                {
                    'match': {
                        'protocolSection.designModule.designInfo.interventionModel': {
                            'query': intervention,
                            'boost': 0.2
                            # 'operator': 'AND'
                        }
                    }
                }
            ]
        }
    }
    return query_dict


def outcome_query(outcome):
    '''
    including primaryOutcomes, secondaryOutcomes, and otherOutcomes
    '''
    query_dict = {
        'bool': {
            'should': [
                {
                    'multi_match': {
                        'query': outcome,
                        'fields': [
                            'protocolSection.outcomesModule.primaryOutcomes.measure^0.3',
                            'protocolSection.outcomesModule.secondaryOutcomes.measure^0.3',
                            'protocolSection.outcomesModule.otherOutcomes.measure^0.3',
                            'protocolSection.outcomesModule.primaryOutcomes.description^0.3',
                            'protocolSection.outcomesModule.secondaryOutcomes.description^0.3',
                            'protocolSection.outcomesModule.otherOutcomes.description^0.3',
                        ],
                    }
                }
            ]
        }
    }
    return query_dict


def location_query(location):
    '''
    location should be one of [CN, US, EU, JP, Other]
    - CN: China, including HK, TW, MO
    - US: United Status
    - EU: European Union countries, including: AT, BE, BG, CY, CZ, DE, DK, EE, EL, ES, FI, FR, HR, HU, IE, IT, LT, LU, LV, MT, NL, PL, PT, RO, SE, SI, SK
    - JP: Japan
    - Other
    '''
    valid_location = ['CN', 'US', 'EU', 'JP', 'Other']
    if location not in valid_location:
        raise ValueError(
            'location should be one of: [{}]'.format(
                ', '.join(valid_location)
            )
        )
    else:
        pass
    query_dict = {
        'bool': {
            'should': list()
        }
    }
    if location == 'CN':
        query_dict['bool']['should'].append(
            {
                'match': {
                    'protocolSection.contactsLocationsModule.locations.country': {
                        'query': 'China Taiwan Hong Kong Macau',
                        'operator': 'OR',
                        'boost': 0.1
                    }
                }
            }
        )
    elif location == 'US':
        query_dict['bool']['should'].append(
            {
                'match': {
                    'protocolSection.contactsLocationsModule.locations.country': {
                        'query': 'United States',
                        'operator': 'AND',
                        'boost': 0.1
                    }
                }
            }
        )
    elif location == 'EU':
        query_dict['bool']['should'].append(
            {
                'match': {
                    'protocolSection.contactsLocationsModule.locations.country': {
                        'query': 'Austria Belgium Bulgaria Cyprus Czech Germany Denmark Estonia Greece Spain Finland France Croatia Hungary Ireland Italy Lithuania Luxembourg Latvia Malta Netherlands Poland Portugal Romania Sweden Slovenia Slovakia',
                        'operator': 'OR',
                        'boost': 0.1
                    }
                }
            }
        )
    elif location == 'JP':
        query_dict['bool']['should'].append(
            {
                'match': {
                    'protocolSection.contactsLocationsModule.locations.country': {
                        'query': 'Japan',
                        'operator': 'AND',
                        'boost': 0.1
                    }
                }
            }
        )
    else:
        raise ValueError(
            'location should be one of: [{}]'.format(', '.join(valid_location))
        )
    return query_dict


def make_query_body(indication: str = None,
                    phase: str = None,
                    treatment_line: str = None,
                    health_condition: str = None,
                    sex: str = None,
                    only_age: str = None,
                    age: str = None,
                    intervention_model: str = None,
                    masking: str = None,
                    outcome: str = None,
                    location: str = None,
                    size=500):
    query_info = dict()
    if indication:
        query_info['indication'] = indication_query(indication)
    else:
        pass
    if phase:
        query_info['phase'] = phase_query(phase)
    else:
        pass
    if treatment_line:
        query_info['treatment_line'] = treatment_line_query(treatment_line.lower())
    else:
        pass
    if health_condition:
        query_info['health_condition'] = health_condition_query(health_condition.lower())
    else:
        pass
    if only_age:
        query_info['age'] = only_age_query(only_age.upper())
    else:
        pass
    if age:
        query_info['age'] = age_query(age.upper())
    else:
        pass
    if sex:
        query_info['sex'] = sex_query(sex.upper())
    else:
        pass
    if intervention_model:
        query_info['intervention'] = intervention_model_query(intervention_model.upper())
    else:
        pass
    if masking:
        query_info['masking'] = masking_query(masking.upper())
    else:
        pass

    must = list()
    must_not = list()
    should = list()

    for label, qd in query_info.items():
        if 'must' in qd['bool']:
            must.extend(qd['bool']['must'])
        else:
            pass
        if 'must_not' in qd['bool']:
            must_not.extend(qd['bool']['must_not'])
        else:
            pass
        if 'should' in qd['bool']:
            should.extend(qd['bool']['should'])
        else:
            pass

    search_body = {
        'query': {
            'bool': dict()
        },
        'size': size,
    }
    if len(must):
        search_body['query']['bool']['must'] = must
    else:
        pass
    if len(must_not):
        search_body['query']['bool']['must_not'] = must_not
    else:
        pass
    if len(should):
        search_body['query']['bool']['should'] = should
    else:
        pass

    return search_body


def module_name_for_table(cls, tablename, table):
    if table.schema is not None:
        return f"{table.schema}"
    else:
        return "default"


def get_brief_outcomes(dbstring, nct_ids):
    engine = sa.create_engine(dbstring)

    NoahBase = automap_base()
    NoahBase.prepare(autoload_with=engine,
                     schema='noah',
                     modulename_for_table=module_name_for_table)

    table_outcome = NoahBase.metadata.tables['noah.patch_clinical_trial_outcome']

    outcomes = defaultdict(list)
    with engine.connect() as conn:
        result_cursor = conn.execute(
            sa.select(table_outcome).where(table_outcome.c.nct_id.in_(nct_ids))
        )
        for a in result_cursor:
            outcomes[a[1]].append(
                dict(zip(['id', 'nct_id', 'outcome', 'outcome_type'], a))
            )
    return outcomes


def format_trial_info(trial):
    simplified = dict()
    # id
    simplified['id'] = trial['id']
    simplified['identificationModule'] = trial['protocolSection']['identificationModule']
    # description
    simplified['description'] = trial['protocolSection'].get(
        'descriptionModule',
        {'detailedDescription': ''}
    ).get('detailedDescription', '')
    if not simplified['description']:
        simplified['description'] = trial['protocolSection'].get(
            'descriptionModule',
            {'briefSummary': ''}
        ).get('briefSummary', '')
    else:
        pass
    # location
    location_dict = {
        'CN': ['China', 'Taiwan', 'Hong Kong', 'Macau'],
        'US': ['United States'],
        'EU': ['Austria', 'Belgium', 'Bulgaria', 'Cyprus', 'Czech Republic', 'Germany',
               'Denmark', 'Estonia', 'Greece', 'Spain', 'Finland', 'France',
               'Croatia', 'Hungary', 'Ireland', 'Italy', 'Lithuania', 'Luxembourg',
               'Latvia', 'Malta', 'Netherlands', 'Poland', 'Portugal', 'Romania',
               'Sweden', 'Slovenia', 'Slovakia'],
        'JP': ['Japan']
    }
    location_2_group = dict()
    for k, v in location_dict.items():
        for name in v:
            location_2_group[name] = k

    if 'contactsLocationsModule' in trial['protocolSection']:
        if 'locations' in trial['protocolSection']['contactsLocationsModule']:
            locations = trial['protocolSection']['contactsLocationsModule']['locations']
        else:
            locations = list()
    else:
        locations = list()

    location_count = Counter()
    for loc in locations:
        country = loc.get('country', '')
        if country in location_2_group:
            location_count[location_2_group[country]] += 1
        else:
            location_count['Other'] += 1
    simplified['location'] = dict(location_count)
    # design
    simplified['designModule'] = trial['protocolSection'].get(
        'designModule', dict()
    )
    # eligibility
    simplified['eligibilityModule'] = trial['protocolSection'].get(
        'eligibilityModule', dict()
    )
    # status
    simplified['statusModule'] = trial['protocolSection'].get(
        'statusModule', dict()
    )
    # outcome
    original_outcome = trial['protocolSection'].get(
        'outcomesModule', dict()
    )
    outcome_measures = defaultdict(list)
    for key in original_outcome:
        for mvalue in original_outcome[key]:
            outcome_measures[key].append(mvalue.get('measure', ''))
    simplified['outcomesModule'] = outcome_measures
    # armsInterventionsModule
    simplified['armsInterventionsModule'] = trial['protocolSection'].get(
        'armsInterventionsModule', dict()
    )
    # sponsorCollaboratorsModule
    simplified['sponsorCollaboratorsModule'] = trial['protocolSection'].get(
        'sponsorCollaboratorsModule', dict()
    )
    # conditionsModule
    simplified['conditionsModule'] = trial['protocolSection'].get(
        'conditionsModule', dict()
    )

    return simplified


def execute_search(es_url,
                   es_username,
                   es_password,
                   index,
                   search_body):
    es_client = elasticsearch.Elasticsearch(
        hosts=es_url,
        basic_auth=(es_username, es_password)
    )
    try:
        search_result = es_client.search(
            index=index,
            body=search_body
        )
    except Exception as e:
        print(e, 'failed retrying')
        es_client = elasticsearch.Elasticsearch(
            hosts=es_url,
            basic_auth=(es_username, es_password)
        )
        search_result = es_client.search(
            index=index,
            body=search_body
        )
    return search_result


def search_trials(indication: str = None,
                  phase: str = None,
                  treatment_line: str = None,
                  health_condition: str = None,
                  sex: str = None,
                  age: str = None,
                  intervention_model: str = None,
                  masking: str = None,
                  outcome: str = None,
                  location: str = None,
                  **kwargs):
    es_url = os.getenv('ES_URL')
    es_username = os.getenv('ES_USERNAME')
    es_password = os.getenv('ES_PASSWORD')
    dbstring = os.getenv('DB_STRING_GOLDEN')
    # first search
    search_body = make_query_body(indication=indication,
                                  phase=phase,
                                  treatment_line=treatment_line,
                                  health_condition=health_condition,
                                  sex=sex,
                                  only_age=age,
                                  intervention_model=intervention_model,
                                  masking=masking,
                                  outcome=outcome,
                                  location=location)
    search_result = execute_search(es_url=es_url,
                                   es_username=es_username,
                                   es_password=es_password,
                                   index='clinicaltrials.gov-clinical_trial',
                                   search_body=search_body)
    hits = search_result['hits']['hits']
    ids_have = [a['_id'] for a in hits]
    # check numbers
    if len(hits) <= 10:
        if phase in ['1/2', '2/3']:
            phases = phase.split('/')
            for p in phases:
                search_body = make_query_body(indication=indication,
                                              phase=p,
                                              treatment_line=treatment_line,
                                              health_condition=health_condition,
                                              sex=sex,
                                              age=age,
                                              intervention_model=intervention_model,
                                              masking=masking,
                                              outcome=outcome,
                                              location=location)
                search_result = execute_search(es_url=es_url,
                                               es_username=es_username,
                                               es_password=es_password,
                                               index='clinicaltrials.gov-clinical_trial',
                                               search_body=search_body)
                for hit in search_result['hits']['hits']:
                    if hit['_id'] not in ids_have:
                        hits.append(hit)
                        ids_have.append(hit['_id'])
                    else:
                        pass
        else:
            search_body = make_query_body(indication=indication,
                                          phase=phase,
                                          treatment_line=treatment_line,
                                          health_condition=health_condition,
                                          sex=sex,
                                          age=age)
            search_result = execute_search(es_url=es_url,
                                           es_username=es_username,
                                           es_password=es_password,
                                           index='clinicaltrials.gov-clinical_trial',
                                           search_body=search_body)
            for hit in search_result['hits']['hits']:
                if hit['_id'] not in ids_have:
                    hits.append(hit)
                    ids_have.append(hit['_id'])
                else:
                    pass
    elif len(hits) > 50:
        old_hits = hits
        hits = list()
        for hit in old_hits:
            criteria_string = hit['_source'].get(
                'protocolSection', {}
            ).get('eligibilityModule', {}).get('eligibilityCriteria', '')
            line_num = inclusion_criteria_numbers(criteria_string)
            if line_num >= 8:
                hits.append(hit)
            else:
                pass
    else:
        # hits 数量在 [10, 50] 之间直接作为结果
        pass
    # format trial dict
    trials = [format_trial_info(hit['_source']) for hit in hits]
    # brief outcome
    if dbstring:
        nct_ids = [trial['identificationModule']['nctId'] for trial in trials]
        brief_measures_dict = get_brief_outcomes(
            dbstring=dbstring,
            nct_ids=nct_ids
        )
        for i, trial in enumerate(trials):
            nct_id = trial['identificationModule']['nctId']
            if nct_id in brief_measures_dict:
                measures_list = brief_measures_dict[nct_id]
                brief_measures = defaultdict(list)
                for mvalue in measures_list:
                    brief_measures[mvalue['outcome_type']].append(
                        mvalue['outcome']
                    )
                if len(brief_measures):
                    trials[i]['outcomesModule'] = brief_measures
                else:
                    pass
        else:
            pass
    else:
        pass

    print("len(trials)", len(trials))
    return trials, kwargs
