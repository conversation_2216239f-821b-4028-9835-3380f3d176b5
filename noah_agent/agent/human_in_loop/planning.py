import asyncio
import io
import time
from typing import List, Type
from agent.human_in_loop.query import run_query
from agent.human_in_loop.constants import background_prefix_map
from agent.explore.mindsearch_workflow_agent import MindSearchWorkflowAgent
from utils.core.get_json_schema import get_openai_json_schema, get_openai_json_schema_v2
from utils.human_in_loop.logprob import process_prob
from tools.human_in_loop.planning.schema import *
from tools.human_in_loop.planning.prompt import *
from agent.core.preset import AgentPreset
from llm.azure_models import GPT4o, Ada
from llm.base_model import BaseLLM
from llm.deepseek_models import HuoshanDeepseekChatR1, DeepseekChatR1
from llm.gcp_models import ClaudeSonnet35, Gemini15Flash, Gemini20Flash
from utils.clinical_utils.clean_args_typesense import clean_args_typesense
import json

MAX_STEPS = 3

tool_mapping = {
    # "NCCN-Guidelines": {"schema": NCCNGuidelinesInputSchema, "prompt": nccn_slot_filling_prompt},
    "General-Inference": {"schema": GeneralInferenceInputSchema},
    "Medical-Search": {"schema": MedicalSearchInputSchema, "prompt": medical_search_slot_filling_prompt},
    "Clinical-Trial-Result-Analysis": {"schema": ClinicalResultsInputSchema, "prompt": clinical_trial_results_slot_filling_prompt},
    "Drug-Analysis": {"schema": DrugCompetitionLandscapeInputSchema, "prompt": drug_competition_landscape_slot_filling_prompt},
    "Catalyst-Event-Analysis": {"schema": CatalystSearchInputSchema, "prompt": catalyst_search_slot_filling_prompt},
    # "Summarize-Results": {"schema": SummarizeResultsInputSchema, "prompt": summarize_results_prompt},
}
output_summary_schema = get_openai_json_schema_v2(OutputSummarySchema)

# class R1PlanningAgent(AgentPreset):
#     llm: BaseLLM = HuoshanDeepseekChatR1
#     backup_llms: List[BaseLLM] = [DeepseekChatR1]
#     sys_prompt: str = """
#     You are an experienced physician responsible for answering patient questions. 
#     You will plan a sequence of tools to use to answer the user prompt.
#     """

class PlanningAgent(AgentPreset):
    llm: BaseLLM = GPT4o
    r1_llm: BaseLLM = HuoshanDeepseekChatR1()
    backup_llms: List[BaseLLM] = []
    sys_prompt: str = """
    You are an experienced physician responsible for answering patient questions. 
    You will plan a sequence of tools to use to answer the user prompt.
    """
    r1beta: bool = False
    
    async def use_tool(self, user_prompt: str, history_messages: List[dict] = [], planning_task: dict = {}, feedback: str = '', hitl_mode:str='', **kwargs):
        original_question = planning_task.get('question', user_prompt)
        body = {
            "history_messages": history_messages,
            "user_prompt": original_question
        }
        past_feedback = planning_task.get('feedback', [])
        total_feedback = past_feedback + [feedback] if feedback else past_feedback
        if total_feedback:
            combo_prompt = {'original_user_prompt': original_question, 'additional_user_prompt': total_feedback}
            body['user_prompt'] = json.dumps(combo_prompt, separators=(',', ': '))
        
        self.llm = self.llm()
        should_replan = True if feedback else False
        language = kwargs.get('language', 'EN')
        plan = planning_task.get('plan', []) 
        version = planning_task.get('version', 0)
        self.r1beta = kwargs.get('r1-beta', False)
        current_tool = {}
        current_step = planning_task.get('current_step', 0)
        hitl_mode = hitl_mode or planning_task.get('hitl_mode', 'always')
        prev_tool_uses = planning_task.get('tool_uses', [])
        if prev_tool_uses and prev_tool_uses[-1].get('complete', False) == False:
            current_tool = prev_tool_uses.pop()
        should_run = bool(plan and not feedback) # Empty feedback signals user confirmation of tool use
        ret = {'tool_uses': [], 'type': 'chat', "agent": "planning", "hitl_mode": hitl_mode, 'sender': 'assistant',
               "current_step": current_step, "current_tool": current_tool, "version": version, 'feedback': total_feedback}
        if current_tool:
            ret['type'] = 'confirmTool'
            ret['sender'] = 'user'
            ret['message'] = feedback
            ret['accept'] = should_run
            yield ret
            ret.pop('accept')
            ret.pop('message')
            ret['sender'] = 'assistant'
            ret['type'] = 'chat'
        elif not plan:
            ret['type'] = 'chat'
            ret['saveChat'] = True
            ret['sender'] = 'user'
            ret['message'] = original_question
            yield ret
            ret.pop('message')
            ret['sender'] = 'assistant'
        if hitl_mode == 'never':
            should_run = True
        ret['saveChat'] = False
        
        if not plan: # if current_tool != plan[0].get('tool')
            if hitl_mode != 'never':
                body['logprobs'] = True
            planning_prompt = self.build_planning_prompt(body['user_prompt'], prev_tool_uses)
            schema = ReplanningInputSchema if prev_tool_uses else PlanningInputSchema
            if self.r1beta:
                # background = replanning_input_prompt if prev_tool_uses else planning_input_prompt
                # response_stream = self.r1_llm.stream_call(user_prompt=background+f"\nUser question: {user_prompt}")
                response_stream = self.r1_llm.stream_call(user_prompt=planning_prompt)
                buffer = io.StringIO()
                async for chunk in response_stream:
                    buffer.write(chunk)
                    ret['message'] = buffer.getvalue()
                    yield ret
                ret['saveChat'] = True
                yield ret 
                ret.pop('message', None)
                value = buffer.getvalue()
                buffer.close()
                planning_prompt = tool_use_extraction_template.format(noah_plan=value)
                # schema.planned_sequence = Field(description=tool_use_extraction_prompt)
            planning_format = get_openai_json_schema_v2(schema)
            plan += (await self.extract_plan(planning_prompt, hitl_mode, prev_tool_uses, planning_format))[:MAX_STEPS-current_step]
            ret['plan'] = plan
            ret['version'] += 1
            ret['type'] = 'planUpdate'
            yield ret
        elif should_replan:
            plan = plan[:current_step]
            planning_prompt = self.build_planning_prompt(body['user_prompt'], prev_tool_uses)
            schema = ReplanningInputSchema if prev_tool_uses else PlanningInputSchema
            if self.r1beta:
                # background = replanning_input_prompt if prev_tool_uses else planning_input_prompt
                # response_stream = self.r1_llm.stream_call(user_prompt=background+f"\nUser question: {user_prompt}")
                response_stream = self.r1_llm.stream_call(user_prompt=planning_prompt)
                buffer = io.StringIO()
                async for chunk in response_stream:
                    buffer.write(chunk)
                    ret['message'] = buffer.getvalue()
                    yield ret
                ret['saveChat'] = True
                yield ret 
                ret.pop('message', None)
                value = buffer.getvalue()
                buffer.close()
                planning_prompt = tool_use_extraction_template.format(noah_plan=value)
                # schema.planned_sequence = Field(description=tool_use_extraction_prompt)
            planning_format = get_openai_json_schema_v2(schema)
            plan += (await self.extract_plan(planning_prompt, hitl_mode, prev_tool_uses, planning_format))[:MAX_STEPS-current_step]
            
            ret['plan'] = plan
            ret['version'] += 1
            ret['type'] = 'planUpdate'
            current_tool = plan[current_step].copy()
            yield ret
        ret.pop("plan", None)
        
        if not plan:
            ret["error"] = "Planning failed"
            yield ret
            return
        
        if current_step >= len(plan):
            ret["error"] = "All steps completed"
            yield ret
            return
        
        if not current_tool:
            current_tool = plan[current_step].copy()
        
        if 'params' not in current_tool:
            prev_data = json.dumps(prev_tool_uses, separators=(',', ': '))
            tool_filling_response = await self.tool_slot_filling(body, current_tool,data=prev_data)
            if hitl_mode != 'never': 
                process_prob(tool_filling_response, details=True)
            tool_filling_msg_json = json.loads(tool_filling_response.choices[0].message.content)

            if current_tool['tool'] in ["Clinical-Trial-Result-Analysis", "Catalyst-Event-Analysis"]:
                tool_filling_msg_json = clean_args_typesense(tool_filling_msg_json)
            elif current_tool['tool'] == "Drug-Analysis":
                tool_filling_msg_json = clean_args_typesense(tool_filling_msg_json, citeline=True)
            print("tool_filling_msg_json", tool_filling_msg_json)
            current_tool['params'] = {k: v for k, v in tool_filling_msg_json.items() if v and k not in ['confidence', 'probabilities']}
            if hitl_mode == 'auto' and plan.get('confidence', 1) < 0.95 or tool_filling_msg_json.get('confidence', 1) < 0.85:
                should_run = False
            
            # ret['type'] = 'tool-update'
            ret['current_tool'] = current_tool
            # yield ret
                
        while should_run:
            ret['type'] = 'statusUpdate'
            ret['agentStatus'] = 'running'
            yield ret
            ret.pop('agentStatus', None)
            ret['saveChat'] = False
            ret['type'] = 'chat'
            # Run the query first to get potential context to select from
            context_data = await run_query(current_tool)

            background_prefix = background_prefix_map[current_tool['tool']] if current_tool['tool'] in background_prefix_map else ''
            step_body = {
                "user_prompt": (current_tool.get('params', None) or {}).get('question',''),
                "history_messages": history_messages,
                "agent":"mindsearchworkflowrefer",
                "params":{
                    "language":language,
                    "model":"deepseek-r1",
                    "enable_rag":False,
                    "background":f"{background_prefix} {context_data}"
                    }
            }
            
            agent = MindSearchWorkflowAgent()
            # buffer = io.StringIO()
            async for chunk in agent.start_wo_dump(**step_body):
                try:
                    current_tool['result'] = chunk['content']
                    ret['current_tool'] = current_tool
                    yield ret
                except Exception as e:
                    print(e)
                # buffer.write(chunk)
                # yield ret
            # value = buffer.getvalue()
            
            ret['saveChat'] = True
            current_tool['status'] = 'done'
            yield ret
            
            ret["tool_uses"].append(current_tool.copy())
            prev_tool_uses.append(current_tool.copy())
            
            ret['type'] = 'planUpdate'
            ret['current_tool'] = {}
            plan[current_step]['status'] = 'done'
            if len(plan) > current_step + 1:
                plan[current_step + 1]['status'] = 'doing'
                plan[current_step + 1]['startedAt'] = int(time.time())
            ret['plan'] = plan
            yield ret
            ret.pop("plan", None)
            current_step += 1
            ret['current_step'] = current_step
            if current_step >= len(plan):
                should_run = False
                break
            if hitl_mode == 'always':
                should_run = False
            if hitl_mode == 'auto' and plan.get('confidence', 1) < 0.95:
                should_run = False
            current_tool = plan[current_step].copy()
            prev_data = json.dumps(prev_tool_uses, separators=(',', ': '))
            tool_filling_response = await self.tool_slot_filling(body, current_tool, data=prev_data)
            tool_filling_msg_json = json.loads(tool_filling_response.choices[0].message.content)
            current_tool['params'] = {k: v for k, v in tool_filling_msg_json.items() if v}
            
            # ret['type'] = 'tool-update'
            ret['current_tool'] = current_tool
            # yield ret
            
            if hitl_mode != 'never': 
                process_prob(tool_filling_response, details=True)
            if hitl_mode == 'auto' and tool_filling_msg_json.get('confidence', 1) < 0.85:
                should_run = False
        if current_step < len(plan):
            ret['type'] = 'statusUpdate'
            ret['agentStatus'] = 'waiting'
        else:
            ret['type'] = 'planUpdate'
            return
        # only replan if new steps have been executed
            
        # await asyncio.gather(*coroutines)
        print("ret", ret)
        yield ret

    async def tool_slot_filling(self, body, current_tool, data=None):
        try:
            tool_name = current_tool['tool']
            tool_info = tool_mapping[tool_name]
            body = body.copy()
            if tool_info['prompt']:
                body['sys_prompt'] = tool_info['prompt']
            planning_format = get_openai_json_schema_v2(tool_info['schema'])
            if data:
                body['user_prompt'] += f"\nPrevious tool use: {data}\n"
            if 'reason' in current_tool and current_tool['reason']:
                body['user_prompt'] += '\nThe goal/reason for choosing this tool: ' + current_tool['reason']
            response = await self.llm.call_response(**body, response_format=planning_format)
            return response
        except Exception as e:
            print("Error in tool_slot_filling", e)
            return
        
    async def query_and_pick(self, tool, **params):
        if tool == "":
            return
            
    async def extract_plan(self, user_prompt, hitl_mode, prior_tool_use = [], planning_format = None):
        response = await self.llm.call_response(user_prompt=user_prompt, response_format=planning_format)
        if hitl_mode != 'never': 
            process_prob(response)
        result = json.loads(response.choices[0].message.content)
        sequence = result.get('planned_sequence', [])[:MAX_STEPS-len(prior_tool_use)]
        for step in sequence:
            step['status'] = 'todo' 
            step['startedAt'] = int(time.time())
        if sequence:
            sequence[0]['status'] = 'doing'
        return sequence
    
    # async def summarize_output(self, output, ret, idx):
    #     response = await self.llm.call_response(sys_prompt="Concisely summarize the output of the tool used", user_prompt=output, response_format=output_summary_schema)
    #     try: summary = json.loads(response.choices[0].message.content)['summary']
    #     except: summary = response.choices[0].message.content
    #     ret["tool_uses"][idx]['summary'] = summary


    def build_planning_prompt(self, user_prompt, prior_tool_use = []):
        user_prompt = 'Make a plan to answer the user question: ' + user_prompt + '\n'
        if prior_tool_use:
            prior_knowledge = 'Tool use history:\n'
            for i, tool_use in enumerate(prior_tool_use):
                prior_knowledge += f"{i+1}: Used tool {tool_use['tool']} to get info: {tool_use['result']}\n"
            user_prompt+='\nPrevious Tool Use:\n'+prior_knowledge
        user_prompt += f"Requirements:\n1. Make each step count and try to use fewer steps if possible, limit the total steps to {MAX_STEPS-len(prior_tool_use)} or less."
        user_prompt += "\n2. Consider the tool use history (if present) and continue the plan based on previous tools used and their results."
        return user_prompt