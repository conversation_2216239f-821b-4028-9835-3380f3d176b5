

import json
from agent.workflow.selection import IdSelectionAgent
from utils.human_in_loop.helpers import function_call_with_retry

async def llm_id_selection(short_data=[], orig_data=[], limit=90000, top_n=10, prompt=''):
    
    prompt_template = f"""
You are an expert in selecting medical items for further processing.
{prompt}
Please help me select appropriate items based on their properties, you can select as much as you deem necessary.
Note that the items we select are only candidates for further processing, not final results, so you can choose potential items of interest.
Don't just choose the ones in the front, also consider items further down the list.
If date requirements not specifed, prioritize items with more recent last updated date.
Select items by ID and return ID list of chosen items.

Items to choose from:
    {json.dumps(short_data, separators=(',', ':'), ensure_ascii=False)}
    """
    result = await function_call_with_retry(IdSelectionAgent().use_tool, user_prompt=prompt_template)
    id_list = result['id_list']
    ret = []
    not_selected_list = []
    cur_len = 0
    for item in orig_data:
        if 'id' in item and (item['id'] in id_list or str(item['id']) in id_list):
            item.pop('id', None)
            cur_len += len(str(item))
            if cur_len > limit:
                break
            ret.append(item)
            continue
        not_selected_list.append(item)
    # for not_selected in not_selected_list:
    #     if 'id' in not_selected:
    #         not_selected.pop('id', None)
    #     cur_len += len(str(not_selected))
    #     if cur_len > limit:
    #         break
    #     ret.append(not_selected)
    return ret
