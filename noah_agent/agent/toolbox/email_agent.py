import time
import logging
from typing import List

from agent.core.preset import AgentPreset

from llm.base_model import BaseLLM
from llm.gcp_models import ClaudeSonnet35
from tools.core.base_tool import BaseTool

logger = logging.getLogger(__name__)


class EmailGeneraterAgent(AgentPreset):
    llm: BaseLLM = ClaudeSonnet35
    sys_prompt: str = "You are an AI medical assistant with extensive clinical experience and professional medical knowledge."
    tools: List[BaseTool] = []
    tool_choice: str = "auto"

    def _format_user_prompt(self, user_prompt: str) -> str:
        email_generater_prompt: str = """请你仔细阅读用户的要求然后根据历史消息中的内容，生成一封email邮件
用户要求:
<user_command>
{user_prompt}
</user_command>

输出结果要求：
1. 请根据用户的要求生成一封可以用于工作中的邮件，要求全文风格一致、逻辑严谨；
2. 请在输出开始处给出邮件标题；
3. 如果用户的要求中没有对方或自己的称呼，请预留出邮件中的对方和自己的称呼的位置；
4. 请在正文开始处给出邮件的内容简介，便于对方快速预览、了解内容；
5. 正文内容逐段详细输出、对于细节请使用bullet；
"""
        return email_generater_prompt.format(user_prompt=user_prompt)

    async def _output(self, user_prompt: str, history_messages: List[dict] = []):
        buffer = ""
        last_yield_time = time.time()
        yield_interval = 0.3 # 调整输出时间间隔(秒)
        llm = self.llm()
        async for chunk in llm.stream_call(sys_prompt=self.sys_prompt, user_prompt=user_prompt, history_messages=history_messages):
            buffer += chunk
            current_time = time.time()

            if current_time - last_yield_time >= yield_interval:
                yield buffer
                buffer = ""
                last_yield_time = current_time
                
        if buffer:
            yield buffer

    async def use_tool(self, user_prompt: str, history_messages: List[dict] = [], images: List[str] = []):
        final_user_prompt = self._format_user_prompt(user_prompt=user_prompt)

        response = {
            "content": "",
            "role": "assistant",
        }
        async for chunk in self._output(user_prompt=final_user_prompt, history_messages=history_messages):
            response['content'] += chunk
            yield response

        logger.info(f"Email output result {response}")
