import re
import copy
import time
import json
import logging
from datetime import datetime
from typing import List

from openai.types.chat import ChatCompletionMessage

import agent.explore.constants as constants
from agent.core.preset import AgentPreset
from agent.explore.schema import SearchNode, MindSearchResponse, SearchType
from agent.explore.mindsearch_agent_v2 import (MindSearchAgentV2)
from llm.base_model import BaseLLM
from llm.composite_models import Compositeo4mini, GPT41
from llm.gcp_models import <PERSON><PERSON>site<PERSON>laude
from tools.core.base_tool import BaseTool
from tools.explore.mindsearch_tools_v2 import (GeneralSearch, WebpageReader, DatastoreFinished)
from tools.explore.workflow_tools import (CatalystEventsDatabaseQuery, ClinicalResultsDatabaseQuery, DrugCompetitionDatabaseQuery)
from agent.explore.mindsearch_workflow_prompt_v2 import (qpt_clinical_results_qr_sys_pt, qpt_catalyst_event_qr_sys_pt,
                                                         qpt_drug_competition_qr_sys_pt, gpt_query_rewrite_user_pt,
                                                         ds_drug_user_pt, ds_catalyst_user_pt, ds_clinical_trail_user_pt)
from utils.tokenizer import tokenizer


logger = logging.getLogger(__name__)

class CatalystEventRewriteAgent(AgentPreset):
    llm: BaseLLM = GPT41
    sys_prompt: str = ''
    tools: List[BaseTool] = [
        CatalystEventsDatabaseQuery,
        GeneralSearch,
        WebpageReader,
        DatastoreFinished
    ]
    tool_choice: str = "required"    


class ClinicalResultsRewriteAgent(AgentPreset):
    llm: BaseLLM = GPT41
    sys_prompt: str = ''
    tools: List[BaseTool] = [
        ClinicalResultsDatabaseQuery,
        GeneralSearch,
        WebpageReader,
        DatastoreFinished
    ]
    tool_choice: str = "required"    


class DrugCompetitionRewriteAgent(AgentPreset):
    llm: BaseLLM = GPT41
    sys_prompt: str = ''
    tools: List[BaseTool] = [
        DrugCompetitionDatabaseQuery,
        GeneralSearch,
        WebpageReader,
        DatastoreFinished
    ]
    tool_choice: str = "required"


class FinalOutputAgent(AgentPreset):
    llm: BaseLLM = CompositeClaude
    sys_prompt: str = ''
    tools: List[BaseTool] = []
    tool_choice: dict = {'type': 'any'}


class DrugCompetitionHitlAgent(MindSearchAgentV2):

    final_output_agent: FinalOutputAgent =  FinalOutputAgent()
    query_rewrite_agent: DrugCompetitionRewriteAgent = DrugCompetitionRewriteAgent()
    rewrite_pt: str = qpt_drug_competition_qr_sys_pt
    final_user_pt: str = ds_drug_user_pt
    query_rewrite_rounds: int = 6

    def _format_qr_prompt(
        self,
        history_search: dict,
        query: str,
        background: str = '',
        language: str = constants.ENGLISH) -> str:

        user_pt = gpt_query_rewrite_user_pt.format(
            current_date=datetime.now().strftime('%Y-%m-%d'),
            language=language,
            history_search=history_search,
            background=background,
            user_question=query,
        )
        return self.rewrite_pt + user_pt

    async def _datastore_query_rewrite(
        self,
        response: MindSearchResponse,
        runtime_info: dict,
        query: str,
        history_messages: List[dict] = ...,
        background: str = '',
        language: str = constants.ENGLISH):

        finished = False

        for _ in range(0, self.query_rewrite_rounds):
            
            # check length
            nsh = self._format_history_action(runtime_info)
            if len(tokenizer.openai(nsh, history_messages)) > 190 * 1000:
                logger.info("chunk size is too big for model, break searching")
                break

            user_prompt = self._format_qr_prompt(nsh, query, background, language)

            node = self._add_thinking_node(response=response, query=query, language=language)

            async for chunk in self.query_rewrite_agent.use_tool(user_prompt=user_prompt, history_messages=history_messages):
                if isinstance(chunk, ChatCompletionMessage):
                    logger.info(f"[_query_rewrite_with_mindsearch] gpt output {chunk}")
                    self._process_chunk(node, chunk, language)
                
                elif isinstance(chunk, dict):    
                    # process fc result
                    runtime_info['history_search'].append(chunk)
                    await self._process_fc_result(chunk, runtime_info, node, language)

                    # break process
                    if chunk.get('function', '') == DatastoreFinished().name:
                        finished = True
            
            if finished:
                break

    def _format_history_action(
        self,
        runtime_info: dict) -> str:

        history_search = copy.deepcopy(runtime_info['history_search'])

        history_search_prompt = ""
        tools = ""
        records = {}
        record_ids = set()

        for index, search in enumerate(history_search, start=1):
            function = search.get('function', '')
            params = search.get('params', {})
            thought_process = params.pop('thought_process', '')
            tool = {'tool_name': function, 'thought_process': thought_process}
            
            if function in [GeneralSearch().name]:
                keywords = []
                for sub_query in search.get('sub_queries', []):
                    keywords.append({
                        "keyword": sub_query.get('key_word', ''),
                        "prefer_region": sub_query.get('region', ''),
                    })
                tool['query'] = keywords
            
            elif function in [CatalystEventsDatabaseQuery().name,
                              ClinicalResultsDatabaseQuery().name,
                              DrugCompetitionDatabaseQuery().name]:
                tool['query'] = params
                results = search.get('result', {}).get('results', [])
                tool['total_fetched'] = len(results)
                for record in results:
                    if record.get('id', '') not in record_ids:
                        record_ids.add(record.get('id', ''))
                        record['tmp_id'] = str(len(record_ids))
                records[index] = results
            
            tools += f"""<step_{index}>
{tool}
</step_{index}>\n"""
        
        history_search_prompt += f"""<tools>
{tools}
</tools>\n"""
        
        source = sorted(list(runtime_info['url_map'].values()), key=lambda x: x.id)
        websearch_results = self._format_source(runtime_info, source)

        history_search_prompt += f"""<websearch_results>
{json.dumps(websearch_results, separators=(',', ':'), ensure_ascii=False)}
</websearch_results>\n"""

        if len(records) > 0:
            records_prompt = ""
            for index, value in records.items():
                records_prompt += f"""<step_{index}>
{json.dumps(list(value), separators=(',', ':'), ensure_ascii=False)}
</step_{index}>
"""
            history_search_prompt += f"""<database_records>
{records_prompt}
</database_records>\n"""

        return history_search_prompt
    
    def _format_final_records(
        self,
        runtime_info: dict):
        records = {}
        select_records = []
        
        for search in runtime_info['history_search']:
            function = search.get('function', '')
            
            if function in [CatalystEventsDatabaseQuery().name, ClinicalResultsDatabaseQuery().name, DrugCompetitionDatabaseQuery().name]:
                for record in search.get('result', {}).get('results', []):
                    if record['id'] not in records:
                        record['tmp_id'] = len(records)
                        records[record['id']] = record

        for search in runtime_info['history_search']:
            if function == DatastoreFinished().name:                
                for record in records.values():
                    if record.get('tmp_id') in search.get('result', []):
                        record.pop('tmp_id', None)
                        select_records.append(record)
        
        # avoid llm missing datafinished node
        if len(select_records) == 0:
            select_records = records
        
        source = []
        url_set = set()

        # Fetch links with content
        for url, item in runtime_info['url_map'].items():
            if url in runtime_info['url_content_map']:
                source.append(item)
                url_set.add(url)

        # Add left links
        for url, item in runtime_info['url_map'].items():
            if url not in url_set:
                source.append(item)

        # format websearch result str
        websearch_results = self._format_source(runtime_info, source)
        
        return f"<records>{json.dumps(select_records, separators=(',', ':'), ensure_ascii=False)}</records>" \
            + f"<websearch_results>{json.dumps(websearch_results, separators=(',', ':'), ensure_ascii=False)}</websearch_results>"


    async def use_tool(self, user_prompt: str, history_messages: List[dict] = [], images: List[str] = [], **kwargs):
        start_time = time.time()

        logger.info(f'User prompt: {user_prompt}')

        # init components
        runtime_info = {
            'history_search': [],
            'url_map': {},
            'url_content_map': {},
        }
        language, background, model, enable_rag = self._init_components(kwargs=kwargs)
        # immediately return an empty node for frontend to show user's question
        response = self.helper.init_response(self)
        yield response
        
        # add thinking node tell user current working
        self._add_thinking_node(response, user_prompt, language)
        yield response

        async for tmp_response in self._task_with_heartbeat(
            response, 
            self._datastore_query_rewrite,
            response,
            runtime_info,
            user_prompt,
            history_messages,
            background,
            language):
            yield tmp_response

        record_results = self._format_final_records(runtime_info)

        final_user_prompt = self.final_user_pt.format(
            current_datetime=datetime.now().strftime('%Y-%m-%d'),
            language=language,
            clinical_trial_data=record_results,
            user_prompt=user_prompt
        )

        async for chunk in self._final_output(user_prompt=final_user_prompt, history_messages=history_messages):
            response.content = chunk
            yield response
            
        self._format_final_output(response=response, language=language, runtime_info=runtime_info)
        yield response
        logger.info(f"MindSearch workflow refer final output: {response.content} {response} cost {time.time() - start_time}s")

    
class CatalystEventHitlAgent(DrugCompetitionHitlAgent):

    final_output_agent: FinalOutputAgent =  FinalOutputAgent()
    query_rewrite_agent: CatalystEventRewriteAgent = CatalystEventRewriteAgent()
    rewrite_pt: str = qpt_catalyst_event_qr_sys_pt
    final_user_pt: str = ds_catalyst_user_pt


class ClinicalResultsHitlAgent(DrugCompetitionHitlAgent):

    final_output_agent: FinalOutputAgent =  FinalOutputAgent()
    query_rewrite_agent: ClinicalResultsRewriteAgent = ClinicalResultsRewriteAgent()
    rewrite_pt: str = qpt_clinical_results_qr_sys_pt
    final_user_pt: str = ds_clinical_trail_user_pt

