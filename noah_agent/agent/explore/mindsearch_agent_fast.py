import copy
import time
import asyncio
import logging
from collections import Counter
from datetime import datetime
from typing import List

from agent.core.preset import AgentPreset
from agent.explore.mindsearch_agent import (MindSearchAgent, MindSearchOfficalSiteQueryRewriteAgent, 
                                            MindSearchOfficalSiteQueryRewriteCNAgent, MindSearchOfficalSiteQueryRewriteJPAgent,
                                            MindSearchPubMedQueryRewriteAgent)
from agent.explore.schema import (MindSearchResponse, SearchNode, SearchType, WebSearchSubject, WebSearchLink, ProcessingType)
from agent.explore.prompt import fast_search_summarize
from agent.explore.helper import (MindSearchHelper)
from tools.explore.mindsearch_tools import (RawQueryWebSearch, )


logger = logging.getLogger(__name__)

class MindSearchFastAgent(MindSearchAgent):

    raw_query_rewrite_agent: MindSearchOfficalSiteQueryRewriteAgent = MindSearchOfficalSiteQueryRewriteAgent()
    pubmed_query_rewrite_agent: MindSearchPubMedQueryRewriteAgent = MindSearchPubMedQueryRewriteAgent()
    raw_query_web_search: RawQueryWebSearch = RawQueryWebSearch()
    mindsearch_helper: MindSearchHelper = MindSearchHelper()

    async def _fetch_search_link_content(self, raw_query: str, node: SearchNode):
        url_choices = [
            {
                "url_id": index,
                "url": search_result.url, 
                "summ": search_result.summ,
                "title": search_result.title,
                "reason": "",
                "is_open": True
            } 
            for index, search_result in enumerate(node.search_results)
        ]
        function_result = None
        try:
            async for chunk in self.search_content_fetcher.run(url_choices=url_choices):
                function_result = chunk
        except Exception as exc:
            logger.warn(f"MindSearch fetch search link content failed, exception {exc}")
        else:
            content_length = 4096 * 4
            if node.subject == WebSearchSubject.DISEASE or node.subject == WebSearchSubject.MEDICINE:
                content_length = 4096 * 2 * 5

            if function_result is not None:
                 for url_choice in function_result:
                        search_result = node.search_results[url_choice['url_id']]
                        search_result.reason = url_choice['reason']
                        search_result.content = url_choice['content'][:content_length]
                        search_result.is_open = True if url_choice['content'] else False
                    
        return node.search_results

    async def _scan_search_link(self, user_prompt: str , children: list[SearchNode]):
        tasks = []
        for child in children:
            if child.search_type == SearchType.WEB:
                tasks.append(self._fetch_search_link_content(user_prompt, child))
            elif child.search_type == SearchType.PUBMED:
                tasks.append(self._fetch_pubmed_abstract(user_prompt, child))

        for completed_task in asyncio.as_completed(tasks):
            await completed_task

        # check children search result
        for child in children:
            if child.search_type == SearchType.WEB:
                child.summary = "DONE"
            elif child.search_type == SearchType.PUBMED:
                child.summary = child.key_word 
        
        return children
    
    def _format_search_results(self, user_prompt: str, children: list[SearchNode]):
        # get search result
        filtered_references = []
        seen_urls = set()

        # get valid web search result
        for child in children:
            if child.search_type == SearchType.WEB:
                valid_results = [result for result in child.search_results if result.is_open]
            
                count = 3
                for result in valid_results:
                    if result.url not in seen_urls and count > 0:
                        filtered_references.append(result)
                        seen_urls.add(result.url)
                        count -= 1

        # if trunk is not enough, extend
        while len(filtered_references) <= 9:
            for child in children:
                valid_results = [result for result in child.search_results if result.is_open and result.url not in seen_urls]
                filtered_references.extend(valid_results)
                seen_urls.update(result.url for result in valid_results)
        
        # if trunk is still not enough, extend page summary
        while len(filtered_references) <= 9:
            for child in children:
                valid_results = [result for result in child.search_results if result.url not in seen_urls]
                filtered_references.extend(valid_results)
                seen_urls.update(result.url for result in valid_results)

        for child in children:
            if child.search_type == SearchType.PUBMED:
                seen_urls.update(result.url for result in child.search_results)

        url_id_map = {url: idx for idx, url in enumerate(seen_urls)}
        url_map = {}
        search_prompt = ""
        for child in children:
            webpage_details = []
            for result in child.search_results:
                if result.url in seen_urls:
                    webpage_details.append(
                        f"<webpage_detail>"
                        f"<url_id>{url_id_map[result.url]}</url_id>"
                        f"<title>{result.title}</title>"
                        f"<url>{result.url}</url>"
                        f"<summ>{result.summ}</summ>"
                        f"<content>{result.content}</content>"
                        f"</webpage_detail>"
                    )
                    url_map[result.url] = {'id': url_id_map[result.url],
                                           'url': result.url,
                                            'title': result.title,
                                            'site_name': result.site_name,
                                            'summary': result.summ,}
            search_prompt += (
                f"<webpage_details>{''.join(webpage_details)}</webpage_details>"
            )

        # clear page content
        for child in children:
            for index, _ in enumerate(child.search_results):
                child.search_results[index].content = ''
        
        return fast_search_summarize.format(user_question=user_prompt,
                                                        web_search=search_prompt,
                                                        language='English'), url_map
    

    async def use_tool(self, user_prompt: str, history_messages: List[dict] = [], images: List[str] = []):
        # check whether need query
        start_time = time.time()
        # query rewrite
        response = self.mindsearch_helper.init_response(self)
        yield response

        response.search_graph = await self._query_rewrite(query=user_prompt, history_messages=copy.deepcopy(history_messages))
        yield response

        if response.search_graph is not None:
            # get sub queries
            response = self.mindsearch_helper.search_processing_stages(self, response)

            for search_node_id, child in enumerate(response.search_graph.children, start=1):
                child.id = search_node_id

            children = await self._scan_search_link(user_prompt=user_prompt, children=response.search_graph.children)

            final_user_prompt, url_map = self._format_search_results(user_prompt=user_prompt, children=children)
            response.search_graph.source = list(url_map.values()) # for output formatting

        else:
            final_user_prompt = f"{user_prompt} ## {self._force_language_output()}"

        # Set socket status as final responsing
        response.processing_type = ProcessingType.RESPONSING
        response.processing_stages.stage_index += 1

        history_messages.append({
            "role": "assistant",
            "content": datetime.now().strftime('The current date is %Y-%m-%d.'),
        })
        #logger.info(f"Mindesearch final response input: {history_messages} {final_user_prompt}")

        async for chunk in self._final_output(response, user_prompt=final_user_prompt, history_messages=history_messages):
            response.content = chunk
            yield response

        response.processing_stages.stage_index += 1
        yield response
        logger.info(f"MindSearch final output: {response.content} {response} cost {time.time() - start_time}s")

        # Add follow update questions.
        response = await self._followup_questions(user_prompt=user_prompt, response=response, history_messages=history_messages)
        yield response

