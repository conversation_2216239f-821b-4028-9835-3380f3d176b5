import re
import time
import copy
import asyncio
import logging
from datetime import datetime
from typing import List, Callable, Any, Optional

from openai.types.chat import ChatCompletionMessage

from agent.core.preset import AgentPreset
from agent.explore.schema import (MindSearchResponse, SearchNode, SearchType)
from agent.explore.mindsearch_agent import MindSearchAgent
from agent.explore.prompt import (system_role, llm_double_check_pt)
from llm.azure_models import GPT4o
from llm.gcp_models import ClaudeSonnet35
from llm.gcp_models import Gemini20Pro
from llm.base_model import BaseLLM
from tools.core.base_tool import BaseTool

logger = logging.getLogger(__name__)


class LlmOutputDoubleCheck(AgentPreset):
    llm: BaseLLM = Gemini20Pro
    sys_prompt: str = system_role
    tools: List[BaseTool] = []
    tool_choice: str = "auto"


class MindSearchDoubleCheckAgent(MindSearchAgent):

    def _init_final_output_agent(self, *arg):
        self.final_output_agent = LlmOutputDoubleCheck()

    def _format_final_prompt(self,
                             query: str,
                             history_messages: List[dict] = [],
                             background: str = '',
                             language: str = 'English'):
        
        
        last_message = history_messages.pop(-1)
        user_prompt = history_messages.pop(-1)
        
        return llm_double_check_pt.format(
            background=background,
            user_prompt=user_prompt['content'],
            llm_output=last_message['content'],
            current_datetime=datetime.now().strftime('%Y-%m-%d.'),
            language=language,
        )


    async def use_tool(self, user_prompt: str, history_messages: List[dict] = [], images: List[str] = [], **kwargs):

        start_time = time.time()

        # init components
        language, background, _, _ = self._init_components(kwargs=kwargs)
        response = self.helper.init_response(self)
        yield response

        final_user_prompt = self._format_final_prompt(query=user_prompt,
                                                      history_messages=history_messages,
                                                      background=background,
                                                      language=language)

        logger.info(f"Mindesearch final response input: {history_messages} {final_user_prompt}")

        async for chunk in self._final_output(user_prompt=final_user_prompt, history_messages=history_messages):
            response.content = chunk
            yield response

        logger.info(f"MindSearch double check final output: {response.content} {response} cost {time.time() - start_time}s")
