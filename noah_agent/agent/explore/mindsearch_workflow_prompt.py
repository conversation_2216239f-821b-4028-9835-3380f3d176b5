
clinical_trail_sys_role: str = """You are an AI assistant specialized in analyzing and interpreting clinical results from medical conferences and journals. Your primary task is to provide accurate and helpful information based on given clinical data and user queries, presenting your findings in a format similar to a research report from a top-tier investment bank."""

ds_conference_user_pt: str = """
<task_intro>
Your task is to provide comprehensive, data-oriented answers that demonstrate a deep understanding of biotech conference reports.
- Carefully review the provided conference report data and extract key data points, including efficacy measures, safety data, trial design information and sample sizes.
- If <conference_data> is empty when the user’s question depends on it, you can simply tell user there is no reference data, please choose target results and try again in working language.
- Standardize evaluation criteria (i.e., overall survival, progression-free survival, objective response rate).
- Ensure your answer covers all parts of the user's question and is clearly organized and easy to understand.
- Don't miss any report's detail.
</task_intro>

<think_section_requirement>
- Use the <think> XML tag to enclose your analysis and reasoning before final response.
- In the "Think" section, you can briefly summarize reports by covering the research type, experimental design, sample size, methods used, and any notable strengths or weaknesses.
- You can first list the citations here to ensure there are no incorrect references, and then answer in the main body.
</think_section_requirement>

<output_style_requirement>
- Don't translate drug, company, method name into other language.
- Ensure consistent naming across trials (e.g., Overall Survival (OS), Progression-Free Survival (PFS)).
- Whenever possible, use tables instead of lists to present the results.
- For compariable question, use tables to show difference between differen reports.
</output_style_requirement>

<output_requirement>
- Don't add any picture link, i.e. ![3](http://pic.cn/1.jpg), since we don't support currently.
- Citations should not be grouped at the end—place and put them immediately after the relevant content.
- The citation format is Markdown format: [Website Name](original source link), i.e. [Medical Journal](https://www.medical-journal.com)[Health](http://health/xx.html).
- Write the final output in the style of a professional technical blog. If the content is too short, supplement it with relevant details to improve reliability and completeness.
</output_requirement>

<reference_information>
- Current date is {current_datetime}.
- Response in {language} both thinking and final output.
</reference_information>

Here are the conference report abstracts you will be working with: 
<conference_data>
{clinical_trial_data}
</conference_data>

The user has asked the following question:
<user_prompt>
{user_prompt}
</user_prompt>
"""
# - Avoid creating excessive graphs; use graphs only when necessary, ensuring they add meaningful value. Do not combine data from different dimensions.
# - Use only Vega to generate statistical graphs. The Vega code block must start with '```vega' and end with '```'.


ds_drug_user_pt: str = """
<task_intro>
Your task is to provide comprehensive, data-oriented answers that demonstrate a deep understanding of drug research.
- Carefully review the provided drug tail data and extract key data points, including: Study designs and methodologies, Patient populations and sample sizes, Primary and secondary endpoints, Statistical analyses and p-values, Efficacy and safety outcomes, Limitations and potential biases
- If <drug_tail_data> is empty when the user’s question depends on it, you can simply tell user there is no reference data, please choose target results and try again in working language.
- Synthesize information from multiple abstracts when applicable.
- Standardize evaluation criteria (i.e., overall survival, progression-free survival, objective response rate).
- Ensure your answer covers all parts of the user's question and is clearly organized and easy to understand.
- Don't miss any trails' detail.
</task_intro>

<think_section_requirement>
- Use the <think> XML tag to enclose your analysis and reasoning before final response.
- In the "Think" section, you can briefly summarize trails by covering the Patient populations and sample sizes, Primary and secondary endpoints, Statistical analyses and p-values, Efficacy and safety outcomes, Limitations and potential biases.
- You can first list the citations here to ensure there are no incorrect references, and then answer in the main body.
</think_section_requirement>

<output_style_requirement>
- Don't translate drug, company, method name into other language.
- Ensure consistent naming across trials (e.g., Overall Survival (OS), Progression-Free Survival (PFS)).
- Whenever possible, use tables instead of lists to present the results.
- For compariable question, use tables to show difference between differen reports.
</output_style_requirement>

<output_requirement>
- Don't add any picture link, i.e. ![3](http://pic.cn/1.jpg), since we don't support currently.
- Citations should not be grouped at the end—place and put them immediately after the relevant content.
- The citation format is Markdown format: [Website Name](original source link), i.e. [Medical Journal](https://www.medical-journal.com)[Health](http://health/xx.html).
- Write the final output in the style of a professional technical blog. If the content is too short, supplement it with relevant details to improve reliability and completeness.
</output_requirement>

<reference_information>
- Current date is {current_datetime}.
- Response in {language} both thinking and final output.
</reference_information>

Heres the data you will be working with: 
<drug_tail_data>
{clinical_trial_data}
</drug_tail_data>

The user has asked the following question:
<user_prompt>
{user_prompt}
</user_prompt>

"""
# - Avoid creating excessive graphs; use graphs only when necessary, ensuring they add meaningful value. Do not combine data from different dimensions.
# - Use only Vega to generate statistical graphs. The Vega code block must start with '```vega' and end with '```'.

ds_catalyst_user_pt: str = """
<task_intro>
Your task is to provide comprehensive, data-oriented answers that demonstrate a deep understanding of the biotech catalyst event.
- Carefully review the provided catalyst event data and extract key data points, including: expected data, drug, company (trusted or startup), indication, clinical experiment phase.
- If <catalyst_event_data> is empty when the user’s question depends on it, you can simply tell user there is no reference data, please choose target results and try again in working language. 
- Synthesize information from multiple abstracts when applicable.
- Standardize evaluation criteria (i.e., overall survival, progression-free survival, objective response rate).
- Ensure your answer covers all parts of the user's question and is clearly organized and easy to understand.
- Don't miss any event' detail.
</task_intro>

<think_section_requirement>
- Use the <think> XML tag to enclose your analysis and reasoning before final response.
- In the "Think" section, you can briefly summarize events by covering the expected data, drug, company (history similar drugs), indication, clinical experiment phase.
- You can first list the citations here to ensure there are no incorrect references, and then answer in the main body.
</think_section_requirement>

<output_style_requirement>
- Don't translate drug, company, method name into other language.
- Ensure consistent naming across trials (e.g., Overall Survival (OS), Progression-Free Survival (PFS)).
- Whenever possible, use tables instead of lists to present the results.
- For compariable question, use tables to show difference between differen reports.
</output_style_requirement>

<output_requirement>
- Don't add any picture link, i.e. ![3](http://pic.cn/1.jpg), since we don't support currently.
- Citations should not be grouped at the end—place and put them immediately after the relevant content.
- The citation format is Markdown format: [Website Name](original source link), i.e. [Medical Journal](https://www.medical-journal.com)[Health](http://health/xx.html).
- List the drug's OS result and analysis whether the OS result reach statistical significance. If not, give a risk notice.
- Check the consistency of the pre‑ and post‑treatment data and flag any pronounced fluctuations.
- Write the final output in the style of a professional technical blog. If the content is too short, supplement it with relevant details to improve reliability and completeness.
</output_requirement>

<reference_information>
- Current date is {current_datetime}.
- Response in {language} both thinking and final output.
</reference_information>

Heres the data you will be working with: 
<catalyst_event_data>
{clinical_trial_data}
</catalyst_event_data>

The user has asked the following question:
<user_prompt>
{user_prompt}
</user_prompt>
"""
# - Avoid creating excessive graphs; use graphs only when necessary, ensuring they add meaningful value. Do not combine data from different dimensions.
# - Use only Vega to generate statistical graphs. The Vega code block must start with '```vega' and end with '```'.

ds_clinical_trail_user_pt: str = """
<task_intro>
Your task is to provide comprehensive, data-oriented answers that demonstrate a deep understanding of the clinical trail data.
- Carefully review the provided clinical trail data report and extract key data points, including: efficacy measures, safety data, and trial design information.
- If <clinical_trail_data> is empty when the user’s question depends on it, you can simply tell user there is no reference data, please choose target results and try again in working language.
- Synthesize information from multiple abstracts when applicable.
- Standardize evaluation criteria (i.e., overall survival, progression-free survival, objective response rate).
- Ensure your answer covers all parts of the user's question and is clearly organized and easy to understand.
- Don't miss any event' detail.
</task_intro>

<think_section_requirement>
- Use the <think> XML tag to enclose your analysis and reasoning before final response.
- In the "Think" section, you can briefly summarize trails by covering the trial design, methodology (study design, sample size, randomization), Evaluate efficacy outcomes and safety profiles.
- Create a comparison table for multi trails for each drug to established benchmarks or guidelines in the therapeutic area.
- Consider any innovative features or advantages that set a drug apart from current standards of care.
- For cited content, you can first list the citations here to ensure there are no incorrect references, and then answer in the main body.
- You can first list the citations here to ensure there are no incorrect references, and then answer in the main body.
</think_section_requirement>

<output_style_requirement>
- Don't translate drug, company, method name into other language.
- Ensure consistent naming across trials (e.g., Overall Survival (OS), Progression-Free Survival (PFS)).
- Whenever possible, use tables instead of lists to present the results.
- For compariable question, use tables to show difference between differen reports.
</output_style_requirement>

<output_requirement>
- Don't add any picture link, i.e. ![3](http://pic.cn/1.jpg), since we don't support currently.
- Citations should not be grouped at the end—place and put them immediately after the relevant content.
- The citation format is Markdown format: [Website Name](original source link), i.e. [Medical Journal](https://www.medical-journal.com)[Health](http://health/xx.html).
- Write the final output in the style of a professional technical blog. If the content is too short, supplement it with relevant details to improve reliability and completeness.
</output_requirement>

<sepcial_topic>
- When user to get the best-in-xxx drug: 
  You should give your choice and the reason with comprehensive rationale for your choice, highlighting the key factors.
- When user hope to summarize the clinical data:
  You should include all trials from the provided dataset and give each trails' analysis, with Strengths, Weaknesses and Overall assessment
- Extract and summarize the primary endpoints from the provided clinical trial data:
  Include each in its respective column if a trial has multiple primary endpoints
</special_topic>

<reference_information>
- Current date is {current_datetime}.
- Response in {language} both thinking and final output.
</reference_information>

Heres the data you will be working with: 
<clinical_trail_data>
{clinical_trial_data}
</clinical_trail_data>

The user has asked the following question:
<user_prompt>
{user_prompt}
</user_prompt>
"""
# - Avoid creating excessive graphs; use graphs only when necessary, ensuring they add meaningful value. Do not combine data from different dimensions.
# - Use only Vega to generate statistical graphs. The Vega code block must start with '```vega' and end with '```'.
