import re
import time
import asyncio
import logging
from datetime import datetime
from typing import List, Callable, Any, Optional

from openai.types.chat import ChatCompletionMessage

import agent.explore.constants as constants

from agent.core.preset import AgentPreset
from agent.explore.schema import (MindSearchResponse, SearchNode, ProcessingType,
                                  SearchType, WebSearchLink)
from agent.explore.mindsearch_agent import (MindSearchOfficialAgent, MindSearchR1FinalOutputAgent)
from agent.explore.mindsearch_pubmed_prompt import (gpt_pubmed_qr_sys_pt, gpt_pubmed_qr_user_pt,
                                                    gpt_pubmed_qr_retry_sys_pt, gpt_pubmed_qr_retry_user_pt,
                                                    ds_pubmed_synthesis_sys_pt, ds_pubmed_synthesis_user_pt,
                                                    r1_pubmed_synthesis_pt)
from agent.explore.prompt import (system_role, picot_query_rewrite_pt,
                                  r1_refer_norag_pt, refer_norag_pt, clinical_trail_sys_role,
                                  r1_pubmed_select_pt, pubmed_select_pt)
from llm.azure_models import GPT4o, GPT41
from llm.deepseek_models import Composite<PERSON><PERSON>seekChat
from llm.base_model import BaseLLM
from tools.core.base_tool import BaseTool
from tools.explore.mindsearch_tools import PubMedPICOTRewrite, PubmedSearch
from utils.scholar import PubMedSearch

logger = logging.getLogger(__name__)


class PubMedPICOTQueryRewriteAgent(AgentPreset):
    llm: BaseLLM = GPT41
    sys_prompt: str = system_role
    tools: List[BaseTool] = [
        PubMedPICOTRewrite
    ]
    tool_choice: str = "required"


class FinalOutputAgent(AgentPreset):
    llm: BaseLLM = CompositeDeepseekChat
    sys_prompt: str = clinical_trail_sys_role
    tools: List[BaseTool] = []


class PubMedSimpleQueryRewriteAgent(AgentPreset):
    llm: BaseLLM = GPT41
    sys_prompt: str = system_role
    tools: List[BaseTool] = [
        PubmedSearch
    ]
    tool_choice: str = "required"


class PubMedSelectAgent(AgentPreset):
    llm: BaseLLM = CompositeDeepseekChat
    sys_prompt: str = system_role
    tools: List[BaseTool] = []


class MindSearchPubMedAgent(MindSearchOfficialAgent):

    pubmed_client: Optional[PubMedSearch] = None    
    pubmed_pico_query_rewrite_agent: PubMedPICOTQueryRewriteAgent = PubMedPICOTQueryRewriteAgent()
    pubmed_simple_query_rewrite_agent: PubMedSimpleQueryRewriteAgent = PubMedSimpleQueryRewriteAgent()
    pubmed_select_agent: PubMedSelectAgent = PubMedSelectAgent()
    final_output_agent: FinalOutputAgent = FinalOutputAgent()
    top_k: int = 80
    batch_count: int = 20
    min_count: int = 0


    def __init__(self, **data):
        super().__init__(**data)
        if self.pubmed_client is None:
            self.pubmed_client = PubMedSearch()

    def _init_final_output_agent(self, enable_rag: bool, model: str):
        if not enable_rag:
            if model == constants.DEEPSEEK_R1:
                self.final_output_agent = MindSearchR1FinalOutputAgent()
                

    def _get_rewrite_tools(self) -> List[Callable]:
        tools = [self._query_rewrite_with_pubmed]    
        return tools

    def _query_rewrite_by_user_input(self,
                                     query: str) -> dict:
        r"""check user input whether has custom search command,
        i.e. [((breast cancer[Title/Abstract]) AND (("1988/07/30"[Date - Create] : "3000"[Date - Create]))) AND (("1988/07/30"[Date - Publication] : "3000"[Date - Publication]))]breast cancer III phase with stomach pain"""
        pattern = r'^\[(.*?)\](.*)$'
        match = re.match(pattern, query)

        query_rewrite = {}

        if match:
            pubmed_query = match.group(1).strip()
            user_prompt = match.group(2).strip()

            query_rewrite = {
                'pubmed_query': pubmed_query,
                'user_prompt': user_prompt,
            }

        return query_rewrite
    
    async def _query_rewrite_by_pico(self,
                                     query: str,
                                     history_messages: List[dict] = [],
                                     background: str = '',
                                     language: str = constants.ENGLISH) -> dict:
        query_rewrite = None

        # use llm rewrite user question
        for _ in range(self.max_retry_times):
            try:
                user_prompt = picot_query_rewrite_pt.format(user_prompt=query,
                                                            current_datetime=datetime.now().strftime('%Y-%m-%d.'))
                logger.info(f"[MindSearchPubMedQueryRewrite] pico user prompt {user_prompt}")
                #print(f"[MindSearchQueryRewrite] input messages {history_messages} {query}")
                #Do not invlove history to avoid complex pubmed search lead no results
                async for chunk in self.pubmed_pico_query_rewrite_agent.use_tool(user_prompt=user_prompt):
                    query_rewrite = chunk
                    if isinstance(query_rewrite, ChatCompletionMessage):
                        logger.info(f"[_query_rewrite_with_pubmed] gpt output {query_rewrite}")
                
                #logger.info(f"[_query_rewrite_with_pubmed] result query: {query} rewrite result: {query_rewrite}")
                # break down retry
                if isinstance(query_rewrite, dict):
                    break

            except Exception as exc:
                logger.warn(f"Mindsearch PICO Pubmed query rewrite failed, raw query {query}", exc)

        return query_rewrite
    
    async def _query_rewrite_by_simple(self,
                                       query: str,
                                       history_messages: List[dict] = [],
                                       background: str = '',
                                       language: str = constants.ENGLISH) -> dict:
        query_rewrite = None

        # use llm rewrite user question
        for _ in range(self.max_retry_times):
            try:
                sys_prompt = gpt_pubmed_qr_sys_pt.format(current_date=datetime.now().strftime('%Y-%m-%d.'),
                                                                    language=language)
                user_prompt = gpt_pubmed_qr_user_pt.format(user_question=query,
                                                               background=background,)
                #Do not invlove history to avoid complex pubmed search lead no results
                self.pubmed_simple_query_rewrite_agent.sys_prompt = sys_prompt
                async for chunk in self.pubmed_simple_query_rewrite_agent.use_tool(user_prompt=user_prompt):
                    query_rewrite = chunk
                    if isinstance(query_rewrite, ChatCompletionMessage):
                        logger.info(f"[_query_rewrite_with_pubmed] gpt output {query_rewrite}")

                #logger.info(f"[_query_rewrite_with_pubmed] result query: {query} rewrite result: {query_rewrite}")
                # break down retry
                if isinstance(query_rewrite, dict):
                    break

            except Exception as exc:
                logger.warn(f"Mindsearch Simple Pubmed query rewrite failed, raw query {query}", exc)

        return query_rewrite
    
    async def _query_rewrite_retrying(self,
                                      historical_queries: List[str],
                                      query: str,
                                      language: str = constants.ENGLISH) -> dict:
        query_rewrite = None

        # use llm rewrite user question
        for _ in range(self.max_retry_times):
            try:
                sys_prompt = gpt_pubmed_qr_retry_sys_pt.format(current_date=datetime.now().strftime('%Y-%m-%d.'),
                                                                language=language)
                user_prompt = gpt_pubmed_qr_retry_user_pt.format(user_question=query,
                                                                 historical_queries=historical_queries)
                #Do not invlove history to avoid complex pubmed search lead no results
                self.pubmed_simple_query_rewrite_agent.sys_prompt = sys_prompt
                #logger.info(f"[MindSearchPubMedQueryRewrite] retry user prompt {rewrite_promot}")
                #Do not invlove history to avoid complex pubmed search lead no results
                async for chunk in self.pubmed_simple_query_rewrite_agent.use_tool(user_prompt=user_prompt):
                    query_rewrite = chunk
                    if isinstance(query_rewrite, ChatCompletionMessage):
                        logger.info(f"[_query_rewrite_with_pubmed] gpt output {query_rewrite}")

                #logger.info(f"[_query_rewrite_with_pubmed] result query: {query} rewrite result: {query_rewrite}")
                # break down retry
                if isinstance(query_rewrite, dict):
                    break

            except Exception as exc:
                logger.warn(f"Mindsearch Pubmed query rewrite retrying failed, raw query {query}", exc)

        return query_rewrite
    
    async def _query_rewrite(self, 
                             response: MindSearchResponse,
                             query_rewrite: dict,
                             query_results: List,
                             query: str,
                             history_messages: List[dict] = ...,
                             background: str = '',
                             language: str = constants.ENGLISH):
        
        historical_queries = []
        
        for _ in range(0, 3):
            # try to rewrite user's question

            # first time try querying rewrite
            if not historical_queries:
                query_rewrite.update(await self._query_rewrite_by_simple(query, history_messages, background, language))
            else:
                query_rewrite.update(await self._query_rewrite_retrying(historical_queries, query, language))
            
            pubmed_query = query_rewrite.get('pubmed_query', '')
            
            if pubmed_query == '':
                # indicate don't need searching
                query_rewrite.clear()
                break

            # remove history data, avoid duplicate record
            query_results.clear()
            query_results.extend(await self.pubmed_client.esearch(query=pubmed_query, top_k=self.top_k))
            historical_queries.append(pubmed_query)
            response.search_graph.children[-1].summary += f"**{pubmed_query}**\n"
            yield response

            logger.info(f"[MindsearchPubmed]_query_rewrite pubmed query: {pubmed_query} get records: {len(query_results)}")
            
            if len(query_results) > self.min_count:
                response.search_graph.children[-1].summary += f"Final fetching count: {len(query_results)}\n"
                response.search_graph.children[-1].processing_type = ProcessingType.DONE
                break
            else:
                response.search_graph.children[-1].summary += f"Total fetching count: {len(query_results)} is too less, try rewriting again ...\n"

            yield response
        
    async def _scan_search_link(self,
                                user_prompt: str,
                                children: list[SearchNode],
                                response: MindSearchResponse,
                                language: str = constants.ENGLISH):
        tasks = []
        for child in children:
            if child.search_type == SearchType.PUBMED:
                tasks.append(self._fetch_pubmed_abstract(user_prompt, child))

        for completed_task in asyncio.as_completed(tasks):
            await completed_task

        return children
    
    def _merge_search_summ(self, children: list[SearchNode], model: str = '') -> tuple[str, dict]:
        url_map = {}
        search_content = ''
        for child in children:
            for search_result in child.search_results:
                index = len(url_map) + 1
                if search_result.url not in url_map:
                    url_map[search_result.url] = self._format_final_source(id=index, search_result=search_result)

                search_content += f"""[webpage {index} begin]
                Title:{search_result.title},
                SCI IF Score: {search_result.cite_score},
                Abstract:{search_result.summ}
                [webpage {index} end]\n"""
        return search_content, url_map
    
    def _format_rag_summary(self, response: MindSearchResponse, model: str = '') -> str:
        search_summary, url_map = self._merge_search_summ(response.search_graph.children, model)
        response.search_graph.source = list(url_map.values()) # for output formatting
        response.search_graph.summary = 'DONE'
        return search_summary
    
    def _query_rewrite_fail(self, response: MindSearchResponse, user_prompt: str, language: str = constants.ENGLISH):
        if response.search_graph:
            for node in response.search_graph.children:
                node.processing_type = ProcessingType.DONE

        if language == constants.CHINESE:
            response.content = f"""当前问题关联搜索: **{user_prompt}** 没有检索到结果，请简化问题或者指定PubMed搜索，如输入如下格式:
[(breast cancer[Title/Abstract]) AND (("2020/12/12"[Date - Create] : "3000"[Date - Create]))] 乳腺癌三期治疗方案"""
        elif language == constants.JAPANESE:
            response.content = f"""現在の質問に関連する検索: **{user_prompt}** では結果が見つかりませんでした。質問を簡略化するか、PubMed検索を指定してください。例えば、以下のフォーマットで入力してください:
[(breast cancer[Title/Abstract]) AND (("2020/12/12"[Date - Create] : "3000"[Date - Create]))] 乳がんIII期の治療計画"""
        elif language == constants.ARABIC:
            response.content = f"""لم يتم العثور على نتائج لعملية البحث المرتبطة بالمشكلة الحالية: **{user_prompt}**. يرجى تبسيط السؤال أو تحديد بحث PubMed باستخدام التنسيق التالي:  
[(breast cancer[Title/Abstract]) AND (("2020/12/12"[Date - Create] : "3000"[Date - Create]))] مثال: خطة علاج سرطان الثدي في المرحلة الثالثة."""
        else:
            response.content = f"""Current query's associated search: **{user_prompt}** returned no results. Please simplify your query or specify a PubMed search, for example, by entering the following format:
[(breast cancer[Title/Abstract]) AND (("2020/12/12"[Date - Create] : "3000"[Date - Create]))] Treatment plan for stage III breast cancer""" 
    
    async def _no_rag_final_output(self,
                                   user_prompt: str,
                                   history_messages: List[dict],
                                   response: MindSearchResponse,
                                   background: str = '',
                                   model: str = '',
                                   language: str = constants.ENGLISH) :
        
        pt_template = r1_refer_norag_pt if model == constants.DEEPSEEK_R1 else refer_norag_pt

        final_user_prompt = pt_template.format(
            background=background,
            user_prompt=user_prompt,
            current_datetime=datetime.now().strftime('%Y-%m-%d.'),
            language=language)

        logger.info(f"Mindesearch refer final response input: {len(history_messages)} {final_user_prompt[:600]}")

        async for chunk in self._final_output(user_prompt=final_user_prompt, history_messages=history_messages):
            response.content = chunk
            yield response
        
        self._format_final_output(response=response)
        yield response
    
    def _format_query_rewrite(self,
                              query_rewrite: dict,
                              response: MindSearchResponse,
                              language: str = constants.ENGLISH):
        pubmed_query = query_rewrite.get('pubmed_query', '')

        def pubmed_query_title(language: str):
            if language == constants.CHINESE:
                return 'PubMed查询命令'
            elif language == constants.JAPANESE:
                return 'PubMed検索コマンド'
            elif language == constants.ARABIC:
                return 'أمر البحث في PubMed'
            else:
                return 'PubMed searching command'
        
        def pubmed_query_though_process(language: str):
            if language == constants.CHINESE:
                return '以下搜索过程基于PubMed检索'
            elif language == constants.JAPANESE:
                return 'PubMed検索コマンド'
            elif language == constants.ARABIC:
                return 'أمر البحث في PubMed'
            else:
                return 'All queries are based on PubMed searching'
            
        if response.search_graph is None:
            response.search_graph = SearchNode(search_type=SearchType.UNKNOWN,
                                               thought_process=pubmed_query_though_process(language))

        node = SearchNode(search_type=SearchType.PUBMED,
                          query=pubmed_query_title(language),
                          summary=f"**{pubmed_query}**" if pubmed_query != '' else '',
                          key_word=pubmed_query,)
        
        response.search_graph.add_child(node)
        return response

    async def _query_pubmed(self,
                            query_rewrite: dict,
                            query_results: List,
                            query: str,
                            history_messages: List[dict] = [],
                            pubmed_results: List[dict] = [],
                            response: MindSearchResponse = None,
                            background: str = '',
                            model:str = '',
                            language: str = constants.ENGLISH):
        
        pubmed_query = query_rewrite.get('pubmed_query', '')
        
        def pubmed_query_init(language: str, index: int):
            start = index * self.batch_count + 1
            end = (index + 1) * self.batch_count
            if language == constants.CHINESE:
                return f'{start}-{end}篇论文PubMed总结...'
            elif language == constants.JAPANESE:
                return f'{start}-{end}PubMed検索...'
            elif language == constants.ARABIC:
                return '...البحث في PubMed'
            else:
                return f'{start}-{end} issues PubMed summarizing ...'
        
        def pubmed_cite_score(language: str, index: int):
            start = index * self.batch_count + 1
            end = (index + 1) * self.batch_count
            if language == constants.CHINESE:
                return f'{start}-{end} 检索结果期刊影响力因子...'
            elif language == constants.JAPANESE:
                return f'{start}-{end} PubMed検索...'
            elif language == constants.ARABIC:
                return f'...البحث في PubMed {end}-{start}'
            else:
                return f'{start}-{end} Retrieval Results Journal Impact Factor ...'

        segments = [query_results[i:i + self.batch_count] for i in range(0, len(query_results), self.batch_count)]

        for index, segment in enumerate(segments):
            # query from pubmed
            citescore_node = SearchNode(search_type=SearchType.PUBMED,
                                        query=pubmed_cite_score(language, index),
                                        key_word=pubmed_query,
                                        processing_type=ProcessingType.PROCESSING)
            
            # display query impact factor
            response.search_graph.add_child(citescore_node)

            summary_node = SearchNode(search_type=SearchType.PUBMED,
                                      query=pubmed_query_init(language=language, index=index),
                                      key_word=pubmed_query,
                                      processing_type=ProcessingType.PROCESSING)

            for value in segment:
                summary_node.add_search_result(self._format_pubmed_weblink(value))
            
            # fetch pubmed abstract
            await self._fetch_pubmed_abstract(query, summary_node)

            # get influence
            found_citation = False
            for item in summary_node.search_results:
                if item.cite_score != '':
                    found_citation = True            
                    citescore_node.summary += f"- **{item.title}** \n *Published in a journal with an impact factor of {item.cite_score}*\n"

            if not found_citation:
                citescore_node.summary = "Fetching journal impact factor failed, please try later."
            
            #update citescore_node status
            citescore_node.processing_type = ProcessingType.DONE

            # read abstract and find most relavent issues
            search_content, _ = self._merge_search_summ(children=[summary_node], model=model)

            response.search_graph.add_child(summary_node)

            # TODO support different model
            pr_prompt = r1_pubmed_select_pt if model == constants.DEEPSEEK_R1 else pubmed_select_pt
            pubmed_select_prompt = pubmed_select_pt.format(web_search=search_content,
                                                    current_date=datetime.now().strftime('%Y-%m-%d.'),
                                                    user_question=query,
                                                    language=language)
            #logger.info(f"PubMed select issues prompt: {pubmed_select_prompt}")

            async for chunk in self.pubmed_select_agent.stream_call(pubmed_select_prompt):
                summary_node.summary += chunk

            self._format_summary_result(summary_node)
            logger.info(f"PubMed select response: {summary_node.summary[-100:]}")

            # get prompting articles     
            recommend_articles = next((item for item in reversed(summary_node.summary.split('\n')) if item), '')
            matches = re.findall(r'(\d+)\((\d+)\)', recommend_articles)
            for match in matches:
                id = int(match[0])
                score = match[1]
                if id > 0 and id <= len(summary_node.search_results):
                    summary_node.search_results[id - 1].score = score
                    pubmed_results.append(summary_node.search_results[id - 1])
        
    async def use_tool(self, user_prompt: str, history_messages: List[dict] = [], images: List[str] = [], **kwargs):
        r"""
        Mindsearch refer agent used for read article, news scenses.
        1. When enable_rag is true, we must search.
        2. The enable_rag is false, the output prompt would be very consice to avoid hallucination
        """
        start_time = time.time()

        # init components
        language, background, model, enable_rag = self._init_components(kwargs=kwargs)
        response = self.helper.init_response(self)
        yield response

        query_rewrite = {}
        query_results = []
        if enable_rag:
            # query rewrite
            # first get query from user input
            query_rewrite = self._query_rewrite_by_user_input(user_prompt)

            # init response search graph
            response = self._format_query_rewrite(query_rewrite, response, language)
            yield response

            # if user set query command, rewrite user_prompt
            if query_rewrite:

                user_prompt = query_rewrite['user_prompt']
                query_results = await self.pubmed_client.esearch(query=query_rewrite['pubmed_query'], top_k=self.top_k)

            else:
                
                async for tmp_response in self._query_rewrite(response, query_rewrite, query_results, user_prompt, history_messages, background, language):
                    yield tmp_response

        # user current question don't need query
        if not query_rewrite:
            # Don't display card content
            response.search_graph.thought_process = ''

            async for response in self._no_rag_final_output(user_prompt, history_messages, response, background, model, language):
                yield response

            self._format_final_output(response=response, model=model)
            logger.info(f"MindSearch PubMed final output: {response.content} cost {time.time() - start_time}s")
            return
        
        if len(query_results) < self.min_count:
            self._query_rewrite_fail(response, user_prompt, language)
            yield response
            logger.info(f"MindSearch PubMed query failed final output {response.content}")
            return
        
        # query pubmed
        pubmed_results = []
        async for tmp_response in self._task_with_heartbeat(response,
                                                            self._query_pubmed,
                                                            query_rewrite,
                                                            query_results,
                                                            user_prompt,
                                                            history_messages,
                                                            pubmed_results,
                                                            response,
                                                            background,
                                                            model,
                                                            language):
            yield tmp_response

        # sort pubmed_results by score
        pubmed_results.sort(key=lambda x: x.score, reverse=True)
        # final output
        search_result, url_map = self._merge_search_summ(children=[SearchNode(search_results=pubmed_results[:40])], model=model)
        response.search_graph.source = list(url_map.values())
        response.search_graph.summary = 'DONE'

        if constants.DEEPSEEK_R1 == model:
            final_user_prompt = r1_pubmed_synthesis_pt.format(
                background=background,
                web_search=search_result,
                user_question=user_prompt,
                current_date=datetime.now().strftime('%Y-%m-%d.'),
                language=language
            )
        else:
            sys_prompt = ds_pubmed_synthesis_sys_pt.format(current_date=datetime.now().strftime('%Y-%m-%d.'),
                                                           language=language)
            final_user_prompt = sys_prompt + ds_pubmed_synthesis_user_pt.format(pubmed_results=search_result,
                                                                                user_question=user_prompt)
        
        logger.info(f"Mindesearch PubMed final response input: {len(history_messages)} {final_user_prompt[:600]}")

        async for chunk in self._final_output(user_prompt=final_user_prompt, history_messages=history_messages):
            response.content = chunk
            yield response
        
        self._format_final_output(response=response, model=model)
        yield response
        logger.info(f"MindSearch PubMed final output: {response.content} cost {time.time() - start_time}s")

