

claude_bk_refer_sys_role: str = r"""You are a medical and pharmaceutical industry writer with extensive knowledge of the field and strong professional writing experience."""

claude_bk_refer_user_pt: str = """
<task_intro>
- Read the history messages and background then answer user's question.
- You can refer to the background which is an article or news.
- The final output should look like a professional technical blog.
</task_intro>

<think_section_requirement>
- Use the <think> XML tag to enclose your analysis and reasoning before final response.
</think_section_requirement>

<output_style_requirement>
- Don't translate drug, company, method name into other language just keep as the original.
- When presenting results, use tables rather than lists whenever possible.
</output_style_requirement>

<output_requirement>
- Please keep the citation in the format [x](url), where x is the cation ID and url is the web link, i.e. [1](https://biomedcentral.com/articles/1).
- If a sentence comes from multiple contexts, list all relevant citation numbers, such as [1](https://biomedcentral.com/articles/1)[2](https://google.com/health).
- Remember not to concentrate all citations at the end, but instead place them right at the corresponding parts of the answer.
- If the content is too short, supplement it with relevant details to improve reliability and completeness.
- Don't add fake url.
</output_requirement>

<reference_information>
- Current date is {current_datetime}.
- The response language is {language}, both think and output.
</reference_information>

Here is the ralted background:
<background>
{background}
<background>

User question:
<user_question>
{user_prompt}
</user_question>
"""
# - Avoid creating excessive graphs; use graphs only when necessary, ensuring they add meaningful value. Do not combine data from different dimensions.
# - Use only Vega to generate statistical graphs. The Vega code block must start with '```vega' and end with '```'.


