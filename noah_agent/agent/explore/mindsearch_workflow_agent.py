import re
import copy
import time
import logging
from ast import literal_eval
from datetime import datetime
from enum import IntEnum
from typing import List

import agent.explore.constants as constants
from agent.core.preset import AgentPreset
from agent.explore.schema import SearchNode, MindSearchResponse, SearchType, ProcessingType
from agent.explore.mindsearch_agent import (MindSearchOfficialAgent, MindSearchR1FinalOutputAgent)
from agent.explore.mindsearch_agent_v2 import (MindSearchAgentV2)
from agent.explore.mindsearch_prompt_v2 import (ds_search_final_output_user_pt)
from agent.explore.prompt import (rag_r1_final_output_pt, r1_refer_norag_pt, r1_refer_norag_catalyst_pt, general_pt)
from agent.explore.mindsearch_workflow_prompt import (clinical_trail_sys_role, ds_conference_user_pt, ds_drug_user_pt,
                                                      ds_catalyst_user_pt, ds_clinical_trail_user_pt)
from llm.base_model import BaseLLM
from llm.azure_models import GPT41, GPTo3
from llm.gcp_models import Compo<PERSON><PERSON>laude
from llm.deepseek_models import CompositeDeepseekChat, CompositeDeepseekReasoner
from tools.core.base_tool import BaseTool
from tools.explore.mindsearch_tools import MindSearchPreTemplate


logger = logging.getLogger(__name__)


class ThemeEnum(IntEnum):
   UNKNOWN = 0
   CLINICAL = 1
   DRUG = 2
   CONFERENCE = 3
   CATALYST = 4


class PreTemplateAgent(AgentPreset):
   llm: BaseLLM = GPT41
   sys_prompt: str = clinical_trail_sys_role
   tools: List[BaseTool] = [
      MindSearchPreTemplate
   ]
   tool_choice: str = "required"


class BackgroundReferFinalOutputAgent(AgentPreset):
   #llm: BaseLLM = CompositeDeepseekChat
   llm: BaseLLM = CompositeClaude
   #llm: BaseLLM = GPTo3
   backup_llms: List[BaseLLM] = [GPT41]
   sys_prompt: str = clinical_trail_sys_role
   tools: List[BaseTool] = []
   tool_choice: str = {'type': 'any'}


class MindSearchWorkflowAgent(MindSearchAgentV2):

   final_output_agent: BackgroundReferFinalOutputAgent = BackgroundReferFinalOutputAgent()
   pre_template_agent: PreTemplateAgent = PreTemplateAgent()

   def _init_final_output_agent(self, enable_rag: bool, model: str):
      if model == constants.DEEPSEEK_R1:
         self.final_output_agent = MindSearchR1FinalOutputAgent()
   
   async def _find_pre_template(self,
                                model: str,
                                user_prompt: str,
                                history_messages: List[dict] = [],
                                background: str = '') -> tuple[str, IntEnum]:
      r"""
      Tell workflow background.
      """
      clinical_background = 'Workflow trail sheet result:'
      clinical_background_0 = 'Selected clinical trial result data:'
      drug_background = 'Selected drug data:'
      conference_background = 'Selected conference data:'
      catalyst_background = 'Selected catalyst data:'
      background = background.strip()
      
      theme = ThemeEnum.UNKNOWN
      template = general_pt
         
      if model == 'deepseek-r1':
         if background.startswith(catalyst_background):
            theme = ThemeEnum.CATALYST
            return r1_refer_norag_catalyst_pt, theme
         return r1_refer_norag_pt, theme
      
      if background.startswith(clinical_background) or background.startswith(clinical_background_0):
         theme = ThemeEnum.CLINICAL
         template = ds_clinical_trail_user_pt
      elif background.startswith(drug_background):
         theme = ThemeEnum.DRUG
         template = ds_drug_user_pt
      elif background.startswith(conference_background):
         theme = ThemeEnum.CONFERENCE
         template = ds_conference_user_pt
      elif background.startswith(catalyst_background):
         theme = ThemeEnum.CATALYST
         template = ds_catalyst_user_pt

      return template, theme

   def _format_llm_input(self,
                         user_prompt_template: str,
                         user_prompt: str,
                         history_messages: List[dict] = [],
                         background: str = '',
                         language: str = 'English',
                         theme_enum: ThemeEnum = ThemeEnum.UNKNOWN) -> tuple[str, List[dict], dict]:
      r"""
      Format llm input, get the background detail from the history messages.
      """
      def extract_background_content(background: str) -> str:
        """Extract and process background content from history messages."""
        # Split by ':' and get content after "<type>:"
        splits = background.split(":", 1)  # limit=1 to split only first occurrences
        return splits[1].strip() if len(splits) >= 2 else background

      background = extract_background_content(background)
    
      formatted_prompt = user_prompt_template.format(
         current_datetime=datetime.now().strftime('%Y-%m-%d.'),
         clinical_trial_data=background,
         background=background,
         user_prompt=user_prompt,
         language=language)
      
      url_map = {}
      if theme_enum == ThemeEnum.CONFERENCE:
         url_map = self.get_url_map(background=background, theme_enum=theme_enum)

      return formatted_prompt, history_messages, url_map
   
   def get_url_map(self, background: str, theme_enum: ThemeEnum) -> dict:
      url_map = {}
      try:
         json_data = literal_eval(background)
         if theme_enum == ThemeEnum.CONFERENCE:
            for index, item in enumerate(json_data, start=1):
               if 'link' in item and item['link']:
                  url = item['link']
                  url_map[url] = {
                     'id': index,
                     'url': url,
                     'site_name': self.helper.get_site_name(url=url, default='Conference'),
                     'title': item.get('citation_title', ''),
                     'summary': item.get('abstract', ''),
                  }
      except Exception as exc:
         logger.warn(f"Parse background failed, exc : {exc}")
      finally:
         return url_map

   async def _final_output(self, user_prompt: str, history_messages: List[dict] = [], theme_enum: ThemeEnum = ThemeEnum.UNKNOWN):
      # TODO
      # So far only GPT4.1 can handl catalyst OS static promble, we change at there.
      if ThemeEnum.CATALYST == theme_enum:
         self.final_output_agent.llm = GPT41()
      
      output = ""
      buffer = ""
      raw_buffer = ""
      last_yield_time = time.time()
      yield_interval = 0.3      
      async for chunk in self.final_output_agent.stream_call(user_prompt=user_prompt, history_messages=history_messages):
         # Check meeting tag start
         raw_buffer += chunk
         buffer += chunk
         
         current_time = time.time()
         if current_time - last_yield_time >= yield_interval:
            output += buffer
            yield output
            buffer = ""
            last_yield_time = current_time

     #logger.info(f"Clinical workflow output {raw_buffer}")
      if buffer:
         buffer = re.sub(r'<clinical_analysis>.*?</clinical_analysis>', '', buffer, flags=re.DOTALL)
         output += buffer
         yield output

   def format_output(self, response: MindSearchResponse, url_map: dict = {}) -> MindSearchResponse:
      # Format output output reference
      content, source = self.helper.format_reference(response.content, url_map)
      response.content = content
      if len(source) > 0:
         response.search_graph = SearchNode()
         response.search_graph.source = source
      return response

   async def use_tool(self, user_prompt: str, history_messages: List[dict] = [], images: List[str] = [], **kwargs):
        r"""
        Mindsearch workflow refer agent used for read clinical trail data, drug trail or Ingredients.
        1. When enable_rag is true, we must search.
        2. We would tell which scense, i.e clinical trail, drug analize, and chose a related template.
        """
        start_time = time.time()

        # init components and get context
        runtime_info = {
            'history_search': [],
            'url_map': {},
            'url_content_map': {},
        }
        language, background, model, enable_rag = self._init_components(kwargs=kwargs)
        response = self.helper.init_response(self)
        yield response

        # query rewrite
        if enable_rag:
            
            async for tmp_response in self._task_with_heartbeat(
                response, 
                self._query_rewrite,
                response,
                runtime_info,
                user_prompt,
                history_messages,
                background,
                language):
                yield tmp_response

            websearch_results, history_messages = self._format_final_searchresults(runtime_info, history_messages)

            # add the related search summary in finally user prompt, performance is batter than add them in the history messages.
            if constants.DEEPSEEK_R1 == model:
                final_user_prompt = rag_r1_final_output_pt.format(
                    background=background,
                    websearch_results=websearch_results,
                    user_prompt=user_prompt,
                    current_datetime=datetime.now().strftime('%Y-%m-%d.'),
                    language=language)
            else:
               final_user_prompt = ds_search_final_output_user_pt.format(
                  current_date=datetime.now().strftime('%Y-%m-%d.'),
                  language=language,
                  background=background,
                  websearch_results=websearch_results,
                  user_question=user_prompt)
        
                
        else:
            self._add_finalout_node(response=response, language=language)
            yield response
            
            user_prompt_template, theme_enum = await self._find_pre_template(
               model=model,
               user_prompt=user_prompt,
               history_messages=history_messages,
               background=background)
               
            final_user_prompt, history_messages, url_map = self._format_llm_input(
               user_prompt_template=user_prompt_template,
               user_prompt=user_prompt,
               history_messages=history_messages,
               background=background,
               theme_enum=theme_enum,
               language=language,)

        logger.info(f"Mindesearch workflow refer final response input: {len(history_messages)} {final_user_prompt[:400]}...{final_user_prompt[-400:]}")

        async for chunk in self._final_output(user_prompt=final_user_prompt, history_messages=history_messages):
            response.content = chunk
            yield response
            
        self._format_final_output(response=response, language=language, runtime_info=runtime_info)
        yield response

        response.processing_type = ProcessingType.RESPONSEDONE
        yield response
        logger.info(f"MindSearch workflow refer final output: {response.content} {response} cost {time.time() - start_time}s")

