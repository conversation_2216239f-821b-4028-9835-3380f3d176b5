import copy
import time
import logging
from datetime import datetime
from enum import IntEnum
from typing import List

import agent.explore.constants as constants
from agent.core.preset import AgentPreset
from agent.explore.schema import SearchType, ProcessingType
from agent.explore.mindsearch_agent import (MindSearchAgent, MindSearchR1FinalOutputAgent)
from agent.explore.mindsearch_agent_v2 import (MindSearchAgentV2)
from agent.explore.mindsearch_prompt_v2 import (ds_search_final_output_user_pt)
from agent.explore.mindsearch_refer_prompt import (claude_bk_refer_sys_role, claude_bk_refer_user_pt)
from agent.explore.prompt import (rag_r1_final_output_pt, r1_refer_norag_pt)
from llm.base_model import BaseLLM
from llm.azure_models import GPT4o, GPT41
from llm.gcp_models import CompositeClaude
from llm.deepseek_models import CompositeDeepseekChat
from tools.core.base_tool import BaseTool


logger = logging.getLogger(__name__)


class BackgroundReferFinalOutputAgent(AgentPreset):
    llm: BaseLLM = CompositeClaude
    backup_llms: List[BaseLLM] = [GPT41]
    sys_prompt: str = claude_bk_refer_sys_role
    tools: List[BaseTool] = []


class MindSearchReferAgent(MindSearchAgentV2):
   
   final_output_agent: BackgroundReferFinalOutputAgent = BackgroundReferFinalOutputAgent()
   
   def _init_final_output_agent(self, enable_rag: bool, model: str):
      if constants.DEEPSEEK_R1 == model:
         self.final_output_agent = MindSearchR1FinalOutputAgent() 

   async def use_tool(self, user_prompt: str, history_messages: List[dict] = [], images: List[str] = [], **kwargs):
        r"""
        Mindsearch refer agent used for read article, news scenses.
        1. When enable_rag is true, we must search.
        2. The enable_rag is false, the output prompt would be very consice to avoid hallucination
        """
        start_time = time.time()

        # init components and get context
        runtime_info = {
            'history_search': [],
            'url_map': {},
            'url_content_map': {},
        }
        language, background, model, enable_rag = self._init_components(kwargs=kwargs)
        response = self.helper.init_response(self)
        yield response

        # query rewrite
        if enable_rag:
            
            async for tmp_response in self._task_with_heartbeat(
                response, 
                self._query_rewrite,
                response,
                runtime_info,
                user_prompt,
                history_messages,
                background,
                language):
                yield tmp_response

            websearch_results = self._format_final_searchresults(runtime_info)

            # add the related search summary in finally user prompt, performance is batter than add them in the history messages.
            if constants.DEEPSEEK_R1 == model:
                final_user_prompt = rag_r1_final_output_pt.format(
                    background=background,
                    websearch_results=websearch_results,
                    user_prompt=user_prompt,
                    current_datetime=datetime.now().strftime('%Y-%m-%d.'),
                    language=language)
            else:
               final_user_prompt = ds_search_final_output_user_pt.format(
                  current_date=datetime.now().strftime('%Y-%m-%d.'),
                  language=language,
                  background=background,
                  websearch_results=websearch_results,
                  user_question=user_prompt)
               
        else:
            pt_template = r1_refer_norag_pt if constants.DEEPSEEK_R1 == model else claude_bk_refer_user_pt
            final_user_prompt = pt_template.format(
                background=background,
                user_prompt=user_prompt,
                current_datetime=datetime.now().strftime('%Y-%m-%d.'),
                language=language)

        self._add_finalout_node(response=response, language=language)
        yield response

        logger.info(f"Mindesearch refer final response input: {len(history_messages)} {final_user_prompt[:400]}...{final_user_prompt[-800:]}")

        async for chunk in self._final_output(user_prompt=final_user_prompt, history_messages=history_messages):
            response.content = chunk
            yield response
        
        self._format_final_output(response=response, language=language, runtime_info=runtime_info)
        yield response

        response.processing_type = ProcessingType.RESPONSEDONE
        yield response
        logger.info(f"MindSearch refer final output: {response.content} {response} cost {time.time() - start_time}s")
