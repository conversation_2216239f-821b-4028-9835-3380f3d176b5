import copy
import time
import asyncio
import logging
import agent.explore.constants as constants

from datetime import datetime
from typing import List
from openai.types.chat import ChatCompletionMessage

from llm.azure_models import GP<PERSON>41, <PERSON>To3, GPTo4<PERSON>ini, Compositeo4mini
from llm.composite_models import GPT41<PERSON><PERSON>seek
from llm.deepseek_models import Composite<PERSON><PERSON>seekReasoner
from llm.base_model import BaseLLM
from agent.core.preset import AgentPreset
from agent.explore.helper import MindSearchHelper
from agent.explore.mindsearch_agent_v2 import MindSearchAgentV2
from agent.explore.mindsearch_finance_prompt import (gpt_query_rewrite_sys_pt, gpt_query_rewrite_user_pt,
                                                     ds_news_synthesis_sys_pt, ds_news_synthesis_user_pt,
                                                     ds_search_final_output_sys_pt, ds_search_final_output_user_pt,
                                                     r1_pubmed_select_pt)
from agent.explore.prompt import (rag_r1_final_output_pt, r1_refer_norag_pt, norag_final_output_pt)
from agent.explore.schema import (MindSearchResponse, SearchNode, SearchType,
                                  ProcessingType, WebSearchLink)
from tools.core.base_tool import BaseTool
from tools.explore.finance_tools import (StockHistoricalPriceQuery, StockNewsQuery, CompanyPressReleasesNewsQuery,
                                         GeneralSearch, CompanyInfoQuery, WebpageReader, Finished,
                                         RecommendNews)
from utils.web_search import ContentFetcher
from utils.tokenizer import tokenizer

logger = logging.getLogger(__name__)


class FinanicalModleingPrepAgent(AgentPreset):
    llm: BaseLLM = Compositeo4mini
    sys_prompt: str = ''
    tools: List[BaseTool] = [
        StockHistoricalPriceQuery,
        StockNewsQuery,
        CompanyPressReleasesNewsQuery,
        GeneralSearch,
        CompanyInfoQuery,
        WebpageReader,
        Finished,
    ]
    tool_choice: str = "required" 


class NewsSelectorAgent(AgentPreset):
    llm: BaseLLM = CompositeDeepseekReasoner
    sys_prompt: str = ''
    tools: List[BaseTool] = []
    tool_choice: str = "required"


class RecommendNewsAgent(AgentPreset):
    llm: BaseLLM = GPT41Deepseek
    sys_prompt: str = ''
    tools: List[BaseTool] = [
        RecommendNews
    ]
    tool_choice: str = "required"


class MindSearchFinanceAgent(MindSearchAgentV2):
    llm: BaseLLM = GPT41Deepseek

    financial_agent: FinanicalModleingPrepAgent = FinanicalModleingPrepAgent()
    news_selector_agent: NewsSelectorAgent = NewsSelectorAgent()
    recommend_news_agent: RecommendNewsAgent = RecommendNewsAgent()
    search_content_fetcher: ContentFetcher = ContentFetcher()
    helper: MindSearchHelper = MindSearchHelper()

    def _init_response(
        self,
        user_prompt: str,
        language: str):
        
        def format_thought_process(language):
            if constants.CHINESE == language:
                return "任务分析与解决..."
            elif constants.JAPANESE == language:
                return "タスクの分析と問題解決..."
            elif constants.ARABIC == language:
                return "تحليل المهام وحل المشكلات..."
            else:
                return "Task Analysis and Problem Solving..."
        
        response = MindSearchResponse(
            processing_type=ProcessingType.PROCESSING,
            search_graph=SearchNode(
                processing_type=ProcessingType.PROCESSING,
                thought_process=format_thought_process(language=language),
                query=user_prompt,
            )
        )

        return response

    async def _query_rewrite(
        self,
        response: MindSearchResponse,
        runtime_info: dict,
        user_prompt: str,
        history_messages: List[dict] = [],
        background: str = '',
        language: str = constants.ENGLISH):

        def format_query(language: str):
            if constants.CHINESE == language:
                return "思考..."
            elif constants.JAPANESE == language:
                return "考え…"
            elif constants.ARABIC == language:
                return "أفكر..."
            else:
                return "Think..."

        history_search = []
        finished = False

        for _ in range(0, 6):
            final_user_prompt = gpt_query_rewrite_sys_pt \
                + gpt_query_rewrite_user_pt.format(
                    current_date=datetime.now().strftime('%Y-%m-%d'),
                    history_search=history_search,
                    user_question=user_prompt,
                    language=language)
            
            node = SearchNode(
                processing_type=ProcessingType.PROCESSING,
                query=format_query(language=language),
            )
            response.search_graph.add_child(node)
            async for chunk in self.financial_agent.use_tool(user_prompt=final_user_prompt, history_messages=history_messages):
                if isinstance(chunk, ChatCompletionMessage):
                    logger.info(f"[_query_rewrite_with_financesearch] gpt output {chunk}")
                elif isinstance(chunk, dict):
                    history_search.append(chunk)
                    self._process_fc_result(chunk, node, history_search, language)
                    
                    # break process
                    if chunk.get('function', '') == Finished().name:
                        finished = True
            
            if finished:
                break
        
        # save history search results
        runtime_info['history_search'] = history_search

    def _process_fc_result(
        self,
        result: dict,
        node: SearchNode,
        history_search: list,
        language: str,
    ):
        function = result.get('function', '')
        params = result.get('params', {})

        def format_link(link: dict):
            return WebSearchLink(
                url=link.get('url', ''),
                title=link.get('title', ''),
                summ=link.get('text', ''),
                type=SearchType.NEWS
            )

        def format_summary(language: str):
            if constants.CHINESE == language:
                return "搜索完成"
            elif constants.JAPANESE == language:
                return "検索完了"
            elif constants.ARABIC == language:
                return "اكتمل البحث"
            else:
                return "Search completed"

        if StockHistoricalPriceQuery().name == function:
            symbol = params.get('symbol', '')
            date_from = params.get('date_from', '')
            date_to = params.get('date_to', '')
            def format_query(language: str):
                if constants.CHINESE == language:
                    return f"获取{symbol}历史股价{date_from} ~ {date_to}"
                elif constants.JAPANESE == language:
                    return f"過去{symbol}の株価を取得する {date_from} ~ {date_to}"
                elif constants.ARABIC == language:
                    return f"الحصول على أسعار الأسهم التاريخية {date_from} ~ {date_to}"
                else:
                    return f"Get {symbol} historical stock prices {date_from} ~ {date_to}" 
            
            node.query = format_query(language=language)
            node.summary = self.format_stockprice_summary(result.get('result', {}))

        elif StockNewsQuery().name == function:
            symbol = params.get('symbol', '')
            date_from = params.get('date_from', '')
            date_to = params.get('date_to', '')
            def format_query(language: str):
                if constants.CHINESE == language:
                    return f"获取{symbol} {date_from} ~ {date_to}最新新闻"
                elif constants.JAPANESE == language:
                    return f"{symbol} {date_from} ~ {date_to}の最新ニュースを取得する"
                elif constants.ARABIC == language:
                    return f"الحصول على أحدث أخبار {symbol} {date_from} ~ {date_to}"
                else:
                    return f"Get the latest news of {symbol} {date_from} ~ {date_to}"  
            
            node.query = format_query(language=language)
            node.search_results = [format_link(link) for link in result.get('result', []) if link.get('url', '') != '']

        elif CompanyPressReleasesNewsQuery().name == function:
            symbol = params.get('symbol', '')
            def format_query(language: str):
                if constants.CHINESE == language:
                    return f"获取{symbol}发布公告"
                elif constants.JAPANESE == language:
                    return f"{symbol}の発表を取得する"
                elif constants.ARABIC == language:
                    return f"الحصول على إعلانات {symbol}"
                else:
                    return f"Get announcements for {symbol}"  
            
            node.query = format_query(language=language)
            node.search_results = [format_link(link) for link in result.get('result', []) if link.get('url', '') != '']
            node.summary = format_summary(language=language)

        elif GeneralSearch().name == function:
            query = params.get('query', '')
            def format_query(language: str):
                if constants.CHINESE == language:
                    return f"查询{query}关联信息"
                elif constants.JAPANESE == language:
                    return f"{query}の関連情報を検索する"
                elif constants.ARABIC == language:
                    return f"البحث عن المعلومات المتعلقة بـ {query}"
                else:
                    return f"Search for related information of {query}"  
            
            node.query = format_query(language=language)
            node.summary = format_summary(language=language)
        
        elif CompanyInfoQuery().name == function:
            symbol = params.get('symbol', '')
            def format_query(language: str):
                if constants.CHINESE == language:
                    return f"获取{symbol}公司信息"
                elif constants.JAPANESE == language:
                    return f"{symbol}の企業情報を取得する"
                elif constants.ARABIC == language:
                    return f"الحصول على معلومات الشركة {symbol}"
                else:
                    return f"Get company information for {symbol}"
            
            node.query = format_query(language=language)
            node.summary = format_summary(language=language)
        
        elif WebpageReader().name == function:
            # Find url related title
            titles = set()
            urls = params.get('urls', [])
            for search in history_search:
                if search.get('function', '') in [StockNewsQuery().name, CompanyPressReleasesNewsQuery().name]:
                    for item in search.get('result', ''):
                        if item.get('url', '') in urls and item.get('title', '') != '':
                            titles.add(item['title'])

            titles_str = ",".join(titles)
            def format_query(language: str):
                if constants.CHINESE == language:
                    return f"阅读网页{titles_str[:20]}...结束"
                elif constants.JAPANESE == language:
                    return f"ウェブページを読む {titles_str[:20]}...終了"
                elif constants.ARABIC == language:
                    return f"قراءة صفحة الويب {titles_str[:20]}...النهاية"
                else:
                    return f"Read webpage {titles_str[:20]}...End"  
            
            node.query = format_query(language=language)            
            node.search_results = [WebSearchLink(url=url, summ='', title='') for url in urls]
            node.summary = format_summary(language=language)
        
        elif Finished().name == function:
            def format_query(language: str):
                if constants.CHINESE == language:
                    return f"搜索结束"
                elif constants.JAPANESE == language:
                    return f"検索終了"
                elif constants.ARABIC == language:
                    return f"العربية"
                else:
                    return f"Search ended"  
            
            node.query = format_query(language=language)            
            node.summary = result.get('result', '')

        node.processing_type = ProcessingType.DONE

    async def _select_news(
        self,
        response: MindSearchResponse,
        user_prompt: str,
        history_messages: List[dict] = [],
        runtime_info: dict = {},
        language: str = constants.ENGLISH):
        
        def format_query(language: str):
            if constants.CHINESE == language:
                return f"筛选重要的新闻内容"
            elif constants.JAPANESE == language:
                return f"重要なニュースコンテンツを抽出する"
            elif constants.ARABIC == language:
                return f"تصفية المحتوى الإخباري المهم"
            else:
                return f"Filter out important news content"  

        citation_id = 1
        url_map = {}
        history_search = []
        node = SearchNode(
            processing_type=ProcessingType.PROCESSING,
            query=format_query(language),
        )
        for result in runtime_info.get('history_search', []):
            function = result.get('function', '')
            if StockNewsQuery().name == function or CompanyPressReleasesNewsQuery().name == function:
                for item in result.get('result', []):
                    url = item.get('url', '')
                    if url == '' or url in url_map:
                        continue
                    
                    item['citation_id'] = citation_id
                    citation_id += 1
                    url_map[url] = item
                    node.search_results.append(WebSearchLink(
                        url=url,
                        summ='',
                        title=item.get('title', ''),
                        type=SearchType.WEB
                    ))
                    
            if WebpageReader().name != function:
                history_search.append(result)

        # no news need to read
        if 1 == citation_id:
            return            

        response.search_graph.add_child(node)

        final_user_prompt = r1_pubmed_select_pt.format(
            current_date=datetime.now().strftime('%Y-%m-%d'),
            language=language,
            user_question=user_prompt,
            history_search=history_search,
        )

        async for chunk in self.news_selector_agent.stream_call(user_prompt=final_user_prompt, history_messages=history_messages,):
            node.summary += chunk

        node = self._format_summary_result(node)

        runtime_info['url_map'] = url_map
        response.search_graph.source = list(url_map.values())

        # function call get selected news
        parsed_news, reading_tips = await self._parse_recommended_news_ids(node.summary, node.search_results)
        runtime_info['recommend_news'] = parsed_news
        runtime_info['history_search'].append({
            'function': 'RecommendWebpagesReadingTips',
            'result': reading_tips
        })

    async def _fetch_news(
        self,
        runtime_info: dict,
    ):
        parsed_news = runtime_info['recommend_news']

        try:
            urls = [search_result.url for search_result in parsed_news if search_result.url != '']
                        
            url_content_map = await self.search_content_fetcher.fetch_urls(urls=urls, type='news')

            url_set = set()
            for result in runtime_info.get('history_search', []):
                function = result.get('function', '')
                if StockNewsQuery().name == function or CompanyPressReleasesNewsQuery().name == function:
                    for item in result.get('result', []):
                        url = item.get('url', '')
                        if url != '' and url not in url_set and url in url_content_map:
                            item['content'] = url_content_map[item.get('url')]
                            url_set.add(url)

        except Exception as exc:
            logger.warn(f"[MindSearchFinance] fetch search link content failed", exc)


    async def _try_select_news(
        self,
        response: MindSearchResponse,
        user_prompt: str,
        history_messages: List[dict] = [],
        runtime_info: dict = {},
        language: str = constants.ENGLISH):

        await self._select_news(
            response=response,
            user_prompt=user_prompt,
            history_messages=history_messages,
            runtime_info=runtime_info,
            language=language)
        
        if 'recommend_news' in runtime_info:
            await self._fetch_news(runtime_info=runtime_info)

    async def _parse_recommended_news_ids(
        self,
        llm_response: str,
        search_results: List[WebSearchLink]) -> List[WebSearchLink]:
        
        result = []
        try:
            user_prompt = f"""Please parse the recommended news ids.
            You can find them at the end of the content, just after keyword: 需要深入阅读的新闻 or need reading news, i.e. 需要深入阅读的新闻:7,2, or Need reading news: 7, 2, 8.
            <content>{llm_response}</content>"""

            async for chunk in self.recommend_news_agent.use_tool(user_prompt=user_prompt):
                result = chunk

        except Exception as exc:
            logger.warn(f"[MindsearchFinance] parse recommended article ids failed, raw query {search_results}", exc)

        logger.info(f"[MindsearchFinance] parse recommended news result: {result}")
        
        res = [search_results[citation_id - 1] for citation_id in result.get('citation_id', '') 
               if 0 <= citation_id - 1 < len(search_results)]
        return res, result.get('reading_tips', '')
    
    def _format_history_search(
        self,
        runtime_info: dict,
    ) -> list:
        # remove urls to avoid citation issue
        history_search = []
        for search in runtime_info.get('history_search', []):
            function = search.get('function', '')
            if 'news' in function.lower():
                new_search = copy.deepcopy(search)
                for item in new_search.get('result', []):
                    item.pop('url', None)
                history_search.append(search)
            else:
                history_search.append(search)
        return history_search

    async def use_tool(self, user_prompt: str, history_messages: List[dict] = [], images: List[str] = [], **kwargs):
        start_time = time.time()
        runtime_info = {}
  
        # init components
        language, background, model, enable_rag = self.helper.get_context(kwargs=kwargs)
        response = self._init_response(user_prompt=user_prompt, language=language)
        yield response

        # query rewrite
        if enable_rag:

            async for tmp_response in self._task_with_heartbeat(
                response, 
                self._query_rewrite,
                response,
                runtime_info,
                user_prompt,
                history_messages,
                background,
                language):
                yield tmp_response

            async for tmp_response in self._task_with_heartbeat(
                response, 
                self._try_select_news,
                response,
                user_prompt,
                history_messages,
                runtime_info,
                language):
                yield tmp_response
            
            # add the related search summary in finally user prompt, performance is batter than add them in the history messages.
            if constants.DEEPSEEK_R1 == model:
                final_user_prompt = rag_r1_final_output_pt.format(
                    background=background,
                    websearch_results=self._format_history_search(runtime_info),
                    user_prompt=user_prompt,
                    current_datetime=datetime.now().strftime('%Y-%m-%d.'),
                    language=language)
            else:
                self.final_output_agent.sys_prompt = ds_search_final_output_sys_pt
                
                final_user_prompt = ds_search_final_output_user_pt.format(current_date=datetime.now().strftime('%Y-%m-%d.'),
                                                                                       language=language,
                                                                                       background=background,
                                                                                       websearch_results=self._format_history_search(runtime_info),
                                                                                       user_question=user_prompt)            
        else:
            pt_template = r1_refer_norag_pt if constants.DEEPSEEK_R1 == model else norag_final_output_pt
            
            final_user_prompt = pt_template.format(
                background=background,
                user_prompt=user_prompt,
                current_datetime=datetime.now().strftime('%Y-%m-%d.'),
                language=language)

        #logger.info(f"Mindesearch final response input: {len(history_messages)} {final_user_prompt}")
        logger.info(f"Mindesearch final response input: {len(history_messages)} {final_user_prompt[:300]}...{final_user_prompt[-600:]}")

        async for chunk in self._final_output(user_prompt=final_user_prompt, history_messages=history_messages):
            response.content = chunk
            yield response
        
        self._format_final_output(response=response, model=model, language=language)
        yield response
        logger.info(f"MindSearch final output: {response.content} cost {time.time() - start_time}s")

