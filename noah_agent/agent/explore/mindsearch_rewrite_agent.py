import time
import asyncio
import logging

from typing import List
from datetime import datetime

import agent.explore.constants as constants
from config import api_config
from agent.core.preset import AgentPreset
from llm.azure_models import GPT41
from llm.composite_models import Compositeo3
from llm.base_model import BaseLLM
from agent.explore.helper import MindSearchHelper
from agent.explore.mindsearch_agent import MindSearchAgent
from agent.explore.schema import (MindSearchResponse, SearchType, SearchNode, ProcessingType)
from agent.explore.mindsearch_rewrite_prompt import (gpt_rewrite_sys_pt, gpt_rewrite_user_pt, rewrite_examples_pt,
                                                     gpt_web_rewrite_sys_pt, gpt_web_rewrite_user_pt,
                                                     gpt_feedback_rewrite_sys_pt, gpt_feedback_rewrite_user_pt)
from tools.core.base_tool import BaseTool
from tools.explore.rewrite_tools import Rewrite
from utils.web_search import GoogleSerperSearch

logger = logging.getLogger(__name__)


class MindSearchRewriteAssistantAgent(AgentPreset):
    llm: BaseLLM = Compositeo3
    sys_prompt: str = ""
    tools: List[BaseTool] = [
        Rewrite
    ]
    tool_choice: str = "required"


class MindSearchRewriteAgent(MindSearchAgent):
    llm: BaseLLM = GPT41

    helper: MindSearchHelper = MindSearchHelper()
    rewrite_agent: MindSearchRewriteAssistantAgent = MindSearchRewriteAssistantAgent()
    web_searcher: GoogleSerperSearch = GoogleSerperSearch(api_key=api_config.GOOGLE_SERPER_API_KEY, top_k=20)

    def _init_components(self, kwargs) -> tuple[str, str]:
        r"""Init agent components by language or whether need rag.
        """

        language, _, _, _ = self.helper.get_context(kwargs=kwargs)
        params = kwargs.get('params', {})
        feedbacks = params.get('feedbacks', [])
        rewrites = params.get('rewrites', [])
        tool_use_context = params.get('tool_use_context', '')

        return language, feedbacks, rewrites, tool_use_context

    async def _first_query_rewrite(
        self,
        query: str,
        tool_use_context: str,
        runtime_info: dict,
        response: MindSearchResponse,
        language: str):

        node = self._add_thinking_node(response, query, language)

        final_user_prompt = gpt_rewrite_sys_pt + rewrite_examples_pt \
            + gpt_rewrite_user_pt.format(
                current_date=datetime.now().strftime('%Y-%m-%d.'),
                language=language,
                user_question=query,
                context=tool_use_context)
        
        rewrite = None
        async for chunk in self.rewrite_agent.use_tool(user_prompt=final_user_prompt):
            rewrite = chunk

        logger.info(f"[MindSearchRewrite] rewrite result {query} {rewrite}")

        node.summary = rewrite.get('reason', '')
        node.processing_type = ProcessingType.DONE

        if rewrite.get('rewrite', '') != '':
            response.content = rewrite['rewrite']
            response.processing_type = ProcessingType.REWRITE
            runtime_info['finished'] = True
        elif rewrite.get('check', '') != '':
            response.content = rewrite['check']

        if len(rewrite.get('sub_queries', [])) > 0:
            runtime_info['need_websearch'] = True

        runtime_info['first_rewrite'] = rewrite

    async def _second_query_rewrite(
        self,
        query: str,
        feedbacks: List[str],
        rewrites: List[str],
        runtime_info: dict,
        response: MindSearchResponse,
        language: str):

        node = self._add_thinking_node(response, query, language)

        user_feedback = ""
        for feedback, rewrite in zip(feedbacks, rewrites):
            user_feedback += f"rewrite:{rewrite}\nuser_feedback:{feedback}"

        final_user_prompt = gpt_feedback_rewrite_sys_pt + rewrite_examples_pt \
            + gpt_feedback_rewrite_user_pt.format(
                current_date=datetime.now().strftime('%Y-%m-%d.'),
                user_feedback=user_feedback,
                language=language,
                user_question=query)
        
        rewrite = ""
        async for chunk in self.rewrite_agent.use_tool(user_prompt=final_user_prompt):
            rewrite = chunk

        logger.info(f"[MindSearchRewrite] rewrite result {query} {rewrite}")

        node.summary = rewrite.get('reason', '')
        node.processing_type = ProcessingType.DONE

        if rewrite.get('rewrite', '') != '':
            response.content = rewrite['rewrite']
            response.processing_type = ProcessingType.REWRITE
            runtime_info['finished'] = True
        else:
            response.content = rewrite['check']

        if len(rewrite.get('sub_queries', [])) > 0:
            runtime_info['need_websearch'] = True

        runtime_info['first_rewrite'] = rewrite

    async def _web_search(
        self,
        runtime_info: dict,
        response: MindSearchResponse,
        language: str):

        rewrite = runtime_info['first_rewrite']

        def format_summary(language: str):
            if language == constants.CHINESE:
                return "搜索完成。"
            elif language == constants.JAPANESE:
                return "検索が完了しました。"
            elif language == constants.ARABIC:
                return " اكتمل البحث."
            else:
                return "Search completed."   

        if len(rewrite.get('sub_queries', [])) == 0:
            return
        
        tasks = []
        for sub_query in rewrite['sub_queries']:
            # TODO 使用不同的engine

            node = SearchNode(
                query=sub_query['sub_query'],
            )
            response.search_graph.add_child(node)
            tasks.append((self.web_searcher.search(query=sub_query.get('keyword_en')), node, sub_query))
                
        for task, node, sub_query in tasks:
            try:
                search_result = await task
                if search_result is not None:
                    sub_query['search_results'] = search_result
                    for value in search_result.values():
                        node.add_search_result(self._format_websearch_weblink(value))
                    node.summary = format_summary(language)
                    node.processing_type = ProcessingType.DONE
            except Exception as exc:
                logger.warning(f"[MindSearchRewrite] query {sub_query['keyword_end']} failed", exc) 

    async def _query_rewrite_with_webknowledge(
        self,
        query: str,
        runtime_info: dict,
        response: MindSearchResponse,
        language: str):

        node = self._add_finalout_node(response, language)

        final_user_prompt = gpt_web_rewrite_sys_pt + rewrite_examples_pt \
            + gpt_web_rewrite_user_pt.format(
                current_date=datetime.now().strftime('%Y-%m-%d.'),
                language=language,
                user_question=query,
                rewrite_result=runtime_info['first_rewrite'],
                web_search=runtime_info['sub_queries'])
        
        rewrite = ""
        async for chunk in self.rewrite_agent.use_tool(user_prompt=final_user_prompt):
            rewrite += chunk

        logger.info(f"[MindSearchRewrite] rewrite with web search result {rewrite}")

        node.summary = rewrite.get('reason', '')
        node.processing_type = ProcessingType.DONE

        if rewrite.get('rewrite', '') != '':
            response.content = rewrite['rewrite']
            response.processing_type = ProcessingType.REWRITE
            runtime_info['finished'] = True

        runtime_info['web_rewrite'] = rewrite     

    async def use_tool(
        self,
        user_prompt: str,
        history_messages: List[dict] = [],
        images: List[str] = [],
        **kwargs):

        start_time = time.time()
        runtime_info = {}

        # init components
        language, feedbacks, rewrites, tool_use_context = self._init_components(kwargs=kwargs)
        response = self.helper.init_response(self)
        logger.info(f"[MindSearchRewrite] {language} {feedbacks} {rewrites} {user_prompt} {kwargs}")

        if len(feedbacks) == 0:
            # try rewrite
            async for tmp_response in self._task_with_heartbeat(
                response,
                self._first_query_rewrite,
                user_prompt,
                tool_use_context,
                runtime_info,
                response,
                language):
                yield tmp_response
        else:
            async for tmp_response in self._task_with_heartbeat(
                response,
                self._second_query_rewrite,
                user_prompt,
                feedbacks,
                rewrites,
                runtime_info,
                response,
                language):
                yield tmp_response

        # need web search
        if not runtime_info.get('finished', False) \
            and len(runtime_info['first_rewrite'].get('sub_queries', [])) > 0:
            
            async for tmp_response in self._task_with_heartbeat(
                response,
                self._web_search,
                runtime_info,
                response,
                language):
                yield tmp_response

            # rewrite with web knowledge
            async for tmp_response in self._task_with_heartbeat(
                response,
                self._query_rewrite_with_webknowledge,
                user_prompt,
                runtime_info,
                response,
                language):
                yield tmp_response
    
        logger.info(f"MindSearch PubMed final output: status: {response.processing_type}, {response.content} ,  cost {time.time() - start_time}s")
