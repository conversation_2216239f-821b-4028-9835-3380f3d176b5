import asyncio
import logging

from datetime import datetime, timedelta
from typing import List, Dict, Tuple, Any, Optional
from pydantic import BaseModel, Field

from tools.core.base_tool import BaseTool
from utils.finance.financialmodelingprep import FinancialModelinGprep
from utils.web_search import ContentFetcher

from config import api_config

logger = logging.getLogger(__name__)


class GeneralSearchInputSchema(BaseModel):
    query: str = Field(
        description='Company name in English or stock symbol, i.e. AA would fetch AAPL, PRAA...'
    )

class GeneralSearch(BaseTool):
    name: str = 'GeneralSearch'
    description: str = 'General search for symbol or company name, result only contains, symbol, name, currency, stock exchange'
    input_schema: BaseModel = GeneralSearchInputSchema
    strict: bool = True   

    fmp_client: FinancialModelinGprep = FinancialModelinGprep()

    async def run(self, **kwarg):
        query = kwarg.get('query', '')

        if query == '':
            yield {}
            return
        
        yield {
            "function": self.name,
            "params": kwarg,
            "result": self.fmp_client.general_search(query=query)
        }

class SymbolInputSchema(BaseModel):
    symbol: str = Field(
        description='Ticker symbol (e.g., "AAPL").'
    )

class StockTimeSpaneQueryInputSchema(BaseModel):
    symbol: str = Field(
        description='Ticker symbol (e.g., "AAPL", "600275.SS").'
    )
    date_from: str = Field(
        description='Start date in YYYY-MM-DD format (e.g., "2023-12-01"). '
                    'If omitted, defaults to six months ago.'
    )
    date_to: str = Field(
        description='End date in YYYY-MM-DD format (e.g., "2024-01-01"). '
                    'If omitted, defaults to today.'
    )


class StockHistoricalPriceQuery(BaseTool):
    name: str = 'StockHistoricalPriceQuery'
    description: str = 'Fetch stock history price by symbol and date spane'
    input_schema: BaseModel = StockTimeSpaneQueryInputSchema
    strict: bool = True   

    fmp_client: FinancialModelinGprep = FinancialModelinGprep()

    async def run(self, **kwarg):
        symbol = kwarg.get('symbol', '')
        date_from = kwarg.get('date_from', (datetime.now() - timedelta(days=180)).strftime('%Y-%m-%d'))
        date_to = kwarg.get('date_to',  datetime.now().strftime('%Y-%m-%d'))

        if symbol == '':
            yield {}
            return
        
        if date_from != '' and date_to != '':
            result = self.fmp_client.daily_char_eod(symbol=symbol, date_from=date_from, date_to=date_to)
        else:
            result = self.fmp_client.daily_char_eod(symbol=symbol)

        yield {
            "function": self.name,
            "params": kwarg,
            "result": result
        }


class StockNewsQuery(BaseTool):
    name: str = 'StockNewsQuery'
    description: str = 'Query stock news by symbol and date spane'
    input_schema: BaseModel = StockTimeSpaneQueryInputSchema
    strict: bool = True   

    fmp_client: FinancialModelinGprep = FinancialModelinGprep()

    async def run(self, **kwarg):
        symbol = kwarg.get('symbol', '')
        date_from = kwarg.get('date_from', (datetime.now() - timedelta(days=180)).strftime('%Y-%m-%d'))
        date_to = kwarg.get('date_to', datetime.now().strftime('%Y-%m-%d'))

        if symbol == '':
            yield {}
            return
        
        if date_from != '' and date_to != '':
            result = self.fmp_client.stock_news(tickers=[symbol], date_from=date_from, date_to=date_to)
        else:
            result = self.fmp_client.stock_news(tickers=[symbol])

        yield {
            "function": self.name,
            "params": kwarg,
            "result": result
        }


class CompanyPressReleasesNewsQuery(BaseTool):
    name: str = 'CompanyPressReleasesNewsQuery'
    description: str = 'Query company press releases news by symbol and date spane'
    input_schema: BaseModel = StockTimeSpaneQueryInputSchema
    strict: bool = True   

    fmp_client: FinancialModelinGprep = FinancialModelinGprep()

    async def run(self, **kwarg):
        symbol = kwarg.get('symbol', '')

        if symbol == '':
            yield {}
            return
        
        result = self.fmp_client.press_releases(symbol=symbol)

        yield {
            "function": self.name,
            "params": kwarg,
            "result": result
        }


class CompanyInfoQuery(BaseTool):
    name: str = 'CompanyInfoQuery'
    description: str = 'Company detail information, i.e. market place, industry, exchange, current price.'
    input_schema: BaseModel = SymbolInputSchema

    fmp_client: FinancialModelinGprep = FinancialModelinGprep()

    async def run(self, **kwarg):
        symbol = kwarg.get('symbol', '')

        yield {
            "function": self.name,
            "params": kwarg,
            "result": self.fmp_client.company_profile(symbol=symbol)
        }
    

class WebpageReaderInputSchema(BaseModel):
    urls: List[str] = Field(
        description='Webpage urls.'
    )


class WebpageReader(BaseTool):
    name: str = 'WebpageReader'
    description: str = 'Webpage reader which can load the webpage content.'
    input_schema: BaseModel = WebpageReaderInputSchema

    content_fetcher: ContentFetcher = ContentFetcher()

    async def run(self, **kwarg):
        urls = kwarg.get('urls', [])

        url_content_map = await self.content_fetcher.fetch_urls(urls=urls)

        yield {
            "function": self.name,
            "params": kwarg,
            "result": url_content_map
        }


class FinishedInputSchema(BaseModel):
    reasoning: str = Field(
        description="Think and search finished reason."
    )


class Finished(BaseTool):
    name: str = 'Finished'
    description: str = 'Finsihment notice function.'
    input_schema: BaseModel = FinishedInputSchema

    async def run(self, **kwarg):
        reasoning = kwarg.get('reasoning', '')

        yield {
            "function": self.name,
            "params": kwarg,
            "result": reasoning
        }


class RecommendNewsInputSchema(BaseModel):
    citation_id: List[int] = Field(
        default=[],
        description='Recommended news page IDs. This field may be empty if no recommendations are available.')
    reading_tips: str = Field(
        description='The important issue or content of the recommended pages need to be readed.'
    )


class RecommendNews(BaseTool):
    name: str = 'RecommendNews'
    description: str = 'Download and read recommended news pages by the news ids.'
    input_schema: BaseModel = RecommendNewsInputSchema
    strict: bool = True

    async def run(self, **kwargs):
        yield kwargs
