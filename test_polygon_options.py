import os
import pandas as pd
import json
from noah_agent.bio_quant.data_sources.polygon_source import PolygonOptionsSource
from noah_agent.bio_quant.analyzers.options_analyzer import OptionsAnalyzer
from noah_agent.bio_quant.models.composite_model import CompositeModel  # or your LLM model

async def test_options_data(symbol: str):
    """Test fetching and analyzing options data for a given symbol"""
    try:
        # Initialize the data source and analyzer
        data_source = PolygonOptionsSource()
        model = CompositeModel()  # or your LLM model
        analyzer = OptionsAnalyzer(model, data_source)
        
        # Create output directory for data files
        output_dir = f"output_{symbol}_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}"
        os.makedirs(output_dir, exist_ok=True)
        data_source.set_output_dir(output_dir)
        analyzer.set_output_dir(output_dir)
        
        print(f"\nFetching options data for {symbol}...")
        
        # Get options data
        options_data = await data_source.get_options_chain(symbol)
        
        # Print basic summary
        print("\nOptions Data Summary:")
        print(f"Stock Price: ${options_data['stock_price']:.2f}" if options_data['stock_price'] else "Stock Price: Not available")
        print(f"Number of Call Options: {len(options_data['calls'])}")
        print(f"Number of Put Options: {len(options_data['puts'])}")
        
        # Prepare data for analyzer
        analysis_input = {
            'symbol': symbol,
            'options_data': options_data,
            'latest_price': options_data['stock_price']
        }
        
        # Get processed options data
        print("\nProcessing options data...")
        processed_data = await analyzer.analyze(analysis_input, only_return_data=True)
        print(processed_data)
        
    except Exception as e:
        print(f"Error: {str(e)}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")

if __name__ == "__main__":
    # Make sure POLYGON_API_KEY environment variable is set
    if not os.getenv('POLYGON_API_KEY'):
        print("Error: POLYGON_API_KEY environment variable not set")
        exit(1)
    
    # Get symbol from command line argument or prompt
    import sys
    if len(sys.argv) > 1:
        symbol = sys.argv[1].upper()
    else:
        symbol = input("Enter stock symbol: ").upper()
    
    asyncio.run(test_options_data(symbol) )