import os
from noah_agent.bio_quant.data_sources import TwitterDataSource
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def format_tweet(tweet: dict) -> str:
    """Format a tweet for display
    
    Args:
        tweet: Tweet data dictionary
        
    Returns:
        Formatted tweet string
    """
    try:
        # Get basic tweet info
        screen_name = tweet.get('screen_name', 'unknown')
        text = tweet.get('text', '')
        created_at = tweet.get('created_at', '')
        
        # Get engagement metrics
        favorites = tweet.get('favorites', 0)
        retweets = tweet.get('retweets', 0)
        replies = tweet.get('replies', 0)
        
        # Try to parse and format date
        try:
            date_obj = datetime.strptime(created_at, '%a %b %d %H:%M:%S +0000 %Y')
            date_str = date_obj.strftime('%Y-%m-%d %H:%M:%S')
        except Exception as e:
            logger.warning(f"Failed to parse date '{created_at}': {e}")
            date_str = created_at
        
        # Format the tweet
        tweet_str = f"""
@{screen_name} · {date_str}
{text}

❤️ {favorites} | 🔄 {retweets} | 💬 {replies}
{'─' * 80}"""
        
        return tweet_str
    except Exception as e:
        logger.error(f"Error formatting tweet: {e}")
        logger.debug(f"Tweet data: {tweet}")
        return "Error formatting tweet"

def main():
    try:
        # Get symbol from user input
        symbol = input("Enter stock symbol (e.g. AAPL): ").strip().upper()
        logger.info(f"Processing symbol: {symbol}")
        
        # Initialize Twitter data source
        try:
            twitter = TwitterDataSource()
        except ValueError as e:
            logger.error(f"Failed to initialize TwitterDataSource: {e}")
            print(f"\nError: {str(e)}")
            print("Please set the TWITTER_API_KEY environment variable")
            return
        
        # Create output directory
        output_dir = f"{symbol}_tweets_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        os.makedirs(output_dir, exist_ok=True)
        twitter.set_output_dir(output_dir)
        logger.debug(f"Created output directory: {output_dir}")
        
        # Get tweets
        print(f"\nFetching tweets for ${symbol}...")
        tweets = twitter.get_tweets(symbol)
        
        # Validate tweets data structure
        if not isinstance(tweets, dict):
            logger.error(f"Invalid tweets data structure. Expected dict, got {type(tweets)}")
            print("Error: Invalid response from Twitter API")
            return
            
        # Process latest tweets
        latest_tweets = tweets.get('latest', [])
        logger.debug(f"Got {len(latest_tweets)} latest tweets")
        
        if latest_tweets:
            print(f"\n📱 Latest Tweets ({len(latest_tweets)})")
            for i, tweet in enumerate(latest_tweets):
                try:
                    print(format_tweet(tweet))
                except Exception as e:
                    logger.error(f"Error processing latest tweet {i}: {e}")
        else:
            print("\nNo latest tweets found")
        
        # Process top tweets
        top_tweets = tweets.get('top', [])
        logger.debug(f"Got {len(top_tweets)} top tweets")
        
        if top_tweets:
            print(f"\n🔥 Top Tweets ({len(top_tweets)})")
            for i, tweet in enumerate(top_tweets):
                try:
                    print(format_tweet(tweet))
                except Exception as e:
                    logger.error(f"Error processing top tweet {i}: {e}")
        else:
            print("\nNo top tweets found")
        
        # Log sample of raw data for debugging
        if latest_tweets:
            logger.debug(f"Sample tweet data: {latest_tweets[0]}")
        
    except Exception as e:
        logger.error(f"Main execution failed: {e}", exc_info=True)
        print(f"\nError: {str(e)}")

if __name__ == "__main__":
    main() 