services:
  noah-agent:
    container_name: noah-agent
    image: noah-agent:0.0.1
    build:
      dockerfile: Dockerfile
      context: ./
    volumes:
      - type: bind
        source: ~/repos/NoahAgent/noah_agent/logs
        target: /noah_agent/logs
      - type: bind
        source: ~/repos/NoahAgent/noah_agent/.env
        target: /noah_agent/.env
      - type: bind
        source: ~/repos//NoahAgent/noah_agent/api.json
        target: /noah_agent/api.json
      - type: bind
        source: ~/repos//NoahAgent/noah_agent/setting_test.json
        target: /noah_agent/setting_test.json
    restart: always
    ports:
      - 127.0.0.1:8011:80
    command: >
      gunicorn main:app --workers 4 --worker-class uvicorn.workers.UvicornWorker --bind 0.0.0.0:80 --preload -c logging_config.py